{"version": 3, "file": "schemaValidator.js", "sourceRoot": "", "sources": ["../../../src/utils/schemaValidator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAE3B,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;AAElC;;GAEG;AACH,MAAM,OAAO,eAAe;IAC1B;;;OAGG;IACH,MAAM,CAAC,QAAQ,CAAC,MAA0B,EAAE,IAAa;QACvD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9C,OAAO,mCAAmC,CAAC;QAC7C,CAAC;QACD,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC9B,OAAO,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,cAAc,CAAC,MAAc;QAC1C,MAAM,SAAS,GAA4B,EAAE,GAAG,MAAM,EAAE,CAAC;QACzD,IAAI,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YACtD,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,SAAS,CAAC,UAAU,IAAI,OAAO,SAAS,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACrE,MAAM,aAAa,GAA4B,EAAE,CAAC;YAClD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,KAAe,CAAC,CAAC;YAC5D,CAAC;YACD,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC;QACvC,CAAC;QACD,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QACxD,CAAC;QACD,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACxB,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF"}