{"version": 3, "file": "ModelStatsDisplay.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/ModelStatsDisplay.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EACL,uBAAuB,EACvB,qBAAqB,EACrB,kBAAkB,GACnB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,eAAe,EAAgB,MAAM,+BAA+B,CAAC;AAE9E,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAC5B,MAAM,eAAe,GAAG,EAAE,CAAC;AAS3B,MAAM,OAAO,GAA2B,CAAC,EACvC,KAAK,EACL,MAAM,EACN,QAAQ,GAAG,KAAK,EAChB,SAAS,GAAG,KAAK,GAClB,EAAE,EAAE,CAAC,CACJ,MAAC,GAAG,eACF,KAAC,GAAG,IAAC,KAAK,EAAE,gBAAgB,YAC1B,KAAC,IAAI,IAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,YACnE,QAAQ,CAAC,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,GAC7B,GACH,EACL,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAC5B,KAAC,GAAG,IAAC,KAAK,EAAE,eAAe,YACzB,KAAC,IAAI,cAAE,KAAK,GAAQ,IADY,KAAK,CAEjC,CACP,CAAC,IACE,CACP,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAa,GAAG,EAAE;IAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,eAAe,EAAE,CAAC;IACpC,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC;IACjC,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAChD,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,CAC/C,CAAC;IAEF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO,CACL,KAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,QAAQ,EAAE,CAAC,EACX,QAAQ,EAAE,CAAC,YAEX,KAAC,IAAI,+DAAoD,GACrD,CACP,CAAC;IACJ,CAAC;IAED,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAEtD,MAAM,cAAc,GAAG,CACrB,MAA8D,EAC9D,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IAExD,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACnC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAC7C,CAAC;IACF,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAC5E,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAC3C,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,aAAa,EAAC,QAAQ,EACtB,QAAQ,EAAE,CAAC,EACX,QAAQ,EAAE,CAAC,aAEX,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,YAAY,sCAE9B,EACP,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAGlB,MAAC,GAAG,eACF,KAAC,GAAG,IAAC,KAAK,EAAE,gBAAgB,YAC1B,KAAC,IAAI,IAAC,IAAI,6BAAc,GACpB,EACL,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CACxB,KAAC,GAAG,IAAC,KAAK,EAAE,eAAe,YACzB,KAAC,IAAI,IAAC,IAAI,kBAAE,IAAI,GAAQ,IADQ,IAAI,CAEhC,CACP,CAAC,IACE,EAGN,KAAC,GAAG,IACF,WAAW,EAAC,QAAQ,EACpB,YAAY,EAAE,IAAI,EAClB,SAAS,EAAE,KAAK,EAChB,UAAU,EAAE,KAAK,EACjB,WAAW,EAAE,KAAK,GAClB,EAGF,KAAC,OAAO,IAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAE,EAAE,EAAE,SAAS,SAAG,EAC7C,KAAC,OAAO,IACN,KAAK,EAAC,UAAU,EAChB,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,GACnE,EACF,KAAC,OAAO,IACN,KAAK,EAAC,QAAQ,EACd,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE;oBAC3B,MAAM,SAAS,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;oBACxC,OAAO,CACL,MAAC,IAAI,IACH,KAAK,EACH,CAAC,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,aAG7D,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,cAAc,EAAE,QAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,UACtD,CACR,CAAC;gBACJ,CAAC,CAAC,GACF,EACF,KAAC,OAAO,IACN,KAAK,EAAC,aAAa,EACnB,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE;oBAC3B,MAAM,UAAU,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;oBAC9C,OAAO,cAAc,CAAC,UAAU,CAAC,CAAC;gBACpC,CAAC,CAAC,GACF,EAEF,KAAC,GAAG,IAAC,MAAM,EAAE,CAAC,GAAI,EAGlB,KAAC,OAAO,IAAC,KAAK,EAAC,QAAQ,EAAC,MAAM,EAAE,EAAE,EAAE,SAAS,SAAG,EAChD,KAAC,OAAO,IACN,KAAK,EAAC,OAAO,EACb,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAC5B,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,YAC7B,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,GAC3B,CACR,CAAC,GACF,EACF,KAAC,OAAO,IACN,KAAK,EAAC,QAAQ,EACd,QAAQ,QACR,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,GAC/D,EACD,SAAS,IAAI,CACZ,KAAC,OAAO,IACN,KAAK,EAAC,QAAQ,EACd,QAAQ,QACR,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE;oBAC3B,MAAM,YAAY,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC;oBAC9C,OAAO,CACL,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,WAAW,aAC5B,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,QAAI,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,UACvD,CACR,CAAC;gBACJ,CAAC,CAAC,GACF,CACH,EACA,WAAW,IAAI,CACd,KAAC,OAAO,IACN,KAAK,EAAC,UAAU,EAChB,QAAQ,QACR,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,GACjE,CACH,EACA,OAAO,IAAI,CACV,KAAC,OAAO,IACN,KAAK,EAAC,MAAM,EACZ,QAAQ,QACR,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,GAC7D,CACH,EACD,KAAC,OAAO,IACN,KAAK,EAAC,QAAQ,EACd,QAAQ,QACR,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,GACnE,IACE,CACP,CAAC;AACJ,CAAC,CAAC"}