{"version": 3, "file": "oauth2.js", "sourceRoot": "", "sources": ["../../../src/code_assist/oauth2.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,YAAY,EAEZ,OAAO,EACP,mBAAmB,GACpB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,GAAG,MAAM,KAAK,CAAC;AACtB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAC3B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,IAAI,MAAM,WAAW,CAAC;AAC7B,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,SAAS,CAAC;AACzC,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EACL,kBAAkB,EAClB,sBAAsB,EACtB,wBAAwB,GACzB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,QAAQ,EAAE,MAAM,6BAA6B,CAAC;AACvD,OAAO,QAAQ,MAAM,eAAe,CAAC;AAErC,wDAAwD;AACxD,MAAM,eAAe,GACnB,0EAA0E,CAAC;AAE7E,0DAA0D;AAC1D,6EAA6E;AAC7E,uFAAuF;AACvF,2EAA2E;AAC3E,4EAA4E;AAC5E,4DAA4D;AAC5D,MAAM,mBAAmB,GAAG,qCAAqC,CAAC;AAElE,6CAA6C;AAC7C,MAAM,WAAW,GAAG;IAClB,gDAAgD;IAChD,gDAAgD;IAChD,kDAAkD;CACnD,CAAC;AAEF,MAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,MAAM,mBAAmB,GACvB,sEAAsE,CAAC;AACzE,MAAM,mBAAmB,GACvB,sEAAsE,CAAC;AAEzE,MAAM,UAAU,GAAG,SAAS,CAAC;AAC7B,MAAM,mBAAmB,GAAG,kBAAkB,CAAC;AAY/C,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,QAAkB,EAClB,MAAc;IAEd,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC;QAC9B,QAAQ,EAAE,eAAe;QACzB,YAAY,EAAE,mBAAmB;KAClC,CAAC,CAAC;IAEH,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAmB,EAAE,EAAE;QAChD,MAAM,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,iEAAiE;IACjE,IAAI,MAAM,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,kCAAkC;QAClC,0DAA0D;QAC1D,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,MAAM,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACtC,CAAC;YAAC,MAAM,CAAC;gBACP,0CAA0C;YAC5C,CAAC;QACH,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,0EAA0E;IAC1E,2EAA2E;IAC3E,oDAAoD;IACpD,IAAI,QAAQ,KAAK,QAAQ,CAAC,WAAW,EAAE,CAAC;QACtC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;YACpE,MAAM,aAAa,GAAG,IAAI,OAAO,CAAC;YAChC,kEAAkE;YAClE,6BAA6B;aAC9B,CAAC,CAAC;YACH,MAAM,aAAa,CAAC,cAAc,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAE1C,wFAAwF;YACxF,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CACb,wKAAwK,eAAe,CACrL,CAAC,CACF,EAAE,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAED,IAAI,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC;QAC1B,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,UAAU,GAAG,CAAC,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,OAAO,GAAG,MAAM,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACzC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CACX,0CAA0C,EAC1C,CAAC,KAAK,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,CAC5C,CAAC;YACJ,CAAC;QACH,CAAC;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,CAAC;QAE3C,6DAA6D;QAC7D,OAAO,CAAC,GAAG,CACT,mCAAmC;YACjC,2DAA2D;YAC3D,6BAA6B,QAAQ,CAAC,OAAO,MAAM,CACtD,CAAC;QACF,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAE7C,MAAM,QAAQ,CAAC,oBAAoB,CAAC;IACtC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,MAAoB;IAClD,MAAM,WAAW,GAAG,sDAAsD,CAAC;IAC3E,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,yBAAyB,EAAE,CAAC;IAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrD,MAAM,OAAO,GAAW,MAAM,CAAC,eAAe,CAAC;QAC7C,YAAY,EAAE,WAAW;QACzB,WAAW,EAAE,SAAS;QACtB,KAAK,EAAE,WAAW;QAClB,qBAAqB,EAAE,mBAAmB,CAAC,IAAI;QAC/C,cAAc,EAAE,YAAY,CAAC,aAAa;QAC1C,KAAK;KACN,CAAC,CAAC;IACH,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrB,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEhB,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,EAAE;QACjD,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;YAClC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;QACH,EAAE,CAAC,QAAQ,CAAC,gCAAgC,EAAE,CAAC,IAAI,EAAE,EAAE;YACrD,EAAE,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC;YACvC,IAAI;YACJ,YAAY,EAAE,YAAY,CAAC,YAAY;YACvC,YAAY,EAAE,WAAW;SAC1B,CAAC,CAAC;QACH,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,MAAM,EAAE,CAAC;QAChB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,MAAoB;IAC7C,MAAM,IAAI,GAAG,MAAM,gBAAgB,EAAE,CAAC;IACtC,MAAM,WAAW,GAAG,oBAAoB,IAAI,iBAAiB,CAAC;IAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrD,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,CAAC;QACrC,YAAY,EAAE,WAAW;QACzB,WAAW,EAAE,SAAS;QACtB,KAAK,EAAE,WAAW;QAClB,KAAK;KACN,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAI,CAAC;gBACH,IAAI,GAAG,CAAC,GAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC/C,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAChE,GAAG,CAAC,GAAG,EAAE,CAAC;oBACV,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtD,CAAC;gBACD,mEAAmE;gBACnE,MAAM,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAI,EAAE,uBAAuB,CAAC,CAAC,YAAY,CAAC;gBACvE,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBACpB,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAChE,GAAG,CAAC,GAAG,EAAE,CAAC;oBAEV,MAAM,CAAC,IAAI,KAAK,CAAC,gCAAgC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvE,CAAC;qBAAM,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,KAAK,EAAE,CAAC;oBACrC,GAAG,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;oBAEhD,MAAM,CAAC,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC;gBAC5D,CAAC;qBAAM,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC;wBACvC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAE;wBACrB,YAAY,EAAE,WAAW;qBAC1B,CAAC,CAAC;oBACH,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;oBAC9B,6DAA6D;oBAC7D,IAAI,CAAC;wBACH,MAAM,qBAAqB,CAAC,MAAM,CAAC,CAAC;oBACtC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CACX,6DAA6D,EAC7D,KAAK,CACN,CAAC;wBACF,gEAAgE;oBAClE,CAAC;oBAED,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAChE,GAAG,CAAC,GAAG,EAAE,CAAC;oBACV,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,MAAM,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,OAAO;QACP,oBAAoB;KACrB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,gBAAgB;IAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE;gBACpB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,EAAsB,CAAC;gBACrD,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACtB,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;gBAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,MAAoB;IACvD,IAAI,CAAC;QACH,MAAM,OAAO,GACX,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,uBAAuB,EAAE,CAAC;QAE1E,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAClD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QAEzC,2DAA2D;QAC3D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,cAAc,EAAE,CAAC;QAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oEAAoE;QACpE,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEjC,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,WAAwB;IACtD,MAAM,QAAQ,GAAG,uBAAuB,EAAE,CAAC;IAC3C,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAE5D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACxD,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,uBAAuB;IAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;AAClE,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,yBAAyB;IAC7C,IAAI,CAAC;QACH,MAAM,EAAE,CAAC,EAAE,CAAC,uBAAuB,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACxD,iEAAiE;QACjE,MAAM,wBAAwB,EAAE,CAAC;IACnC,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,WAAW;IACb,CAAC;AACH,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,MAAoB;IACvD,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,cAAc,EAAE,CAAC;QAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,+CAA+C,EAC/C;YACE,OAAO,EAAE;gBACP,aAAa,EAAE,UAAU,KAAK,EAAE;aACjC;SACF,CACF,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,CACX,4BAA4B,EAC5B,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,UAAU,CACpB,CAAC;YACF,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACvC,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;AACH,CAAC"}