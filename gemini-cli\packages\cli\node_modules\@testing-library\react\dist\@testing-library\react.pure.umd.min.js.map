{"version": 3, "file": "react.pure.umd.min.js", "sources": ["../../src/act-compat.js", "../../src/fire-event.js", "../../src/config.js", "../../src/pure.js"], "sourcesContent": ["import * as React from 'react'\nimport * as DeprecatedReactTestUtils from 'react-dom/test-utils'\n\nconst reactAct =\n  typeof React.act === 'function' ? React.act : DeprecatedReactTestUtils.act\n\nfunction getGlobalThis() {\n  /* istanbul ignore else */\n  if (typeof globalThis !== 'undefined') {\n    return globalThis\n  }\n  /* istanbul ignore next */\n  if (typeof self !== 'undefined') {\n    return self\n  }\n  /* istanbul ignore next */\n  if (typeof window !== 'undefined') {\n    return window\n  }\n  /* istanbul ignore next */\n  if (typeof global !== 'undefined') {\n    return global\n  }\n  /* istanbul ignore next */\n  throw new Error('unable to locate global object')\n}\n\nfunction setIsReactActEnvironment(isReactActEnvironment) {\n  getGlobalThis().IS_REACT_ACT_ENVIRONMENT = isReactActEnvironment\n}\n\nfunction getIsReactActEnvironment() {\n  return getGlobalThis().IS_REACT_ACT_ENVIRONMENT\n}\n\nfunction withGlobalActEnvironment(actImplementation) {\n  return callback => {\n    const previousActEnvironment = getIsReactActEnvironment()\n    setIsReactActEnvironment(true)\n    try {\n      // The return value of `act` is always a thenable.\n      let callbackNeedsToBeAwaited = false\n      const actResult = actImplementation(() => {\n        const result = callback()\n        if (\n          result !== null &&\n          typeof result === 'object' &&\n          typeof result.then === 'function'\n        ) {\n          callbackNeedsToBeAwaited = true\n        }\n        return result\n      })\n      if (callbackNeedsToBeAwaited) {\n        const thenable = actResult\n        return {\n          then: (resolve, reject) => {\n            thenable.then(\n              returnValue => {\n                setIsReactActEnvironment(previousActEnvironment)\n                resolve(returnValue)\n              },\n              error => {\n                setIsReactActEnvironment(previousActEnvironment)\n                reject(error)\n              },\n            )\n          },\n        }\n      } else {\n        setIsReactActEnvironment(previousActEnvironment)\n        return actResult\n      }\n    } catch (error) {\n      // Can't be a `finally {}` block since we don't know if we have to immediately restore IS_REACT_ACT_ENVIRONMENT\n      // or if we have to await the callback first.\n      setIsReactActEnvironment(previousActEnvironment)\n      throw error\n    }\n  }\n}\n\nconst act = withGlobalActEnvironment(reactAct)\n\nexport default act\nexport {\n  setIsReactActEnvironment as setReactActEnvironment,\n  getIsReactActEnvironment,\n}\n\n/* eslint no-console:0 */\n", "import {fireEvent as dtlFireEvent} from '@testing-library/dom'\n\n// react-testing-library's version of fireEvent will call\n// dom-testing-library's version of fireEvent. The reason\n// we make this distinction however is because we have\n// a few extra events that work a bit differently\nconst fireEvent = (...args) => dtlFireEvent(...args)\n\nObject.keys(dtlFireEvent).forEach(key => {\n  fireEvent[key] = (...args) => dtlFireEvent[key](...args)\n})\n\n// React event system tracks native mouseOver/mouseOut events for\n// running onMouseEnter/onMouseLeave handlers\n// @link https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/events/EnterLeaveEventPlugin.js#L24-L31\nconst mouseEnter = fireEvent.mouseEnter\nconst mouseLeave = fireEvent.mouseLeave\nfireEvent.mouseEnter = (...args) => {\n  mouseEnter(...args)\n  return fireEvent.mouseOver(...args)\n}\nfireEvent.mouseLeave = (...args) => {\n  mouseLeave(...args)\n  return fireEvent.mouseOut(...args)\n}\n\nconst pointerEnter = fireEvent.pointerEnter\nconst pointerLeave = fireEvent.pointerLeave\nfireEvent.pointerEnter = (...args) => {\n  pointerEnter(...args)\n  return fireEvent.pointerOver(...args)\n}\nfireEvent.pointerLeave = (...args) => {\n  pointerLeave(...args)\n  return fireEvent.pointerOut(...args)\n}\n\nconst select = fireEvent.select\nfireEvent.select = (node, init) => {\n  select(node, init)\n  // React tracks this event only on focused inputs\n  node.focus()\n\n  // React creates this event when one of the following native events happens\n  // - contextMenu\n  // - mouseUp\n  // - dragEnd\n  // - keyUp\n  // - keyDown\n  // so we can use any here\n  // @link https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/events/SelectEventPlugin.js#L203-L224\n  fireEvent.keyUp(node, init)\n}\n\n// React event system tracks native focusout/focusin events for\n// running blur/focus handlers\n// @link https://github.com/facebook/react/pull/19186\nconst blur = fireEvent.blur\nconst focus = fireEvent.focus\nfireEvent.blur = (...args) => {\n  fireEvent.focusOut(...args)\n  return blur(...args)\n}\nfireEvent.focus = (...args) => {\n  fireEvent.focusIn(...args)\n  return focus(...args)\n}\n\nexport {fireEvent}\n", "import {\n  getConfig as getConfigDTL,\n  configure as configureDTL,\n} from '@testing-library/dom'\n\nlet configForRTL = {\n  reactStrictMode: false,\n}\n\nfunction getConfig() {\n  return {\n    ...getConfigDTL(),\n    ...configForRTL,\n  }\n}\n\nfunction configure(newConfig) {\n  if (typeof newConfig === 'function') {\n    // Pass the existing config out to the provided function\n    // and accept a delta in return\n    newConfig = newConfig(getConfig())\n  }\n\n  const {reactStrictMode, ...configForDTL} = newConfig\n\n  configureDTL(configForDTL)\n\n  configForRTL = {\n    ...configForRTL,\n    reactStrictMode,\n  }\n}\n\nexport {getConfig, configure}\n", "import * as React from 'react'\nimport ReactDOM from 'react-dom'\nimport * as ReactDOMClient from 'react-dom/client'\nimport {\n  getQueriesForElement,\n  prettyDOM,\n  configure as configureDTL,\n} from '@testing-library/dom'\nimport act, {\n  getIsReactActEnvironment,\n  setReactActEnvironment,\n} from './act-compat'\nimport {fireEvent} from './fire-event'\nimport {getConfig, configure} from './config'\n\nfunction jestFakeTimersAreEnabled() {\n  /* istanbul ignore else */\n  if (typeof jest !== 'undefined' && jest !== null) {\n    return (\n      // legacy timers\n      setTimeout._isMockFunction === true || // modern timers\n      // eslint-disable-next-line prefer-object-has-own -- No Object.hasOwn in all target environments we support.\n      Object.prototype.hasOwnProperty.call(setTimeout, 'clock')\n    )\n  } // istanbul ignore next\n\n  return false\n}\n\nconfigureDTL({\n  unstable_advanceTimersWrapper: cb => {\n    return act(cb)\n  },\n  // We just want to run `waitFor` without IS_REACT_ACT_ENVIRONMENT\n  // But that's not necessarily how `asyncWrapper` is used since it's a public method.\n  // Let's just hope nobody else is using it.\n  asyncWrapper: async cb => {\n    const previousActEnvironment = getIsReactActEnvironment()\n    setReactActEnvironment(false)\n    try {\n      const result = await cb()\n      // Drain microtask queue.\n      // Otherwise we'll restore the previous act() environment, before we resolve the `waitFor` call.\n      // The caller would have no chance to wrap the in-flight Promises in `act()`\n      await new Promise(resolve => {\n        setTimeout(() => {\n          resolve()\n        }, 0)\n\n        if (jestFakeTimersAreEnabled()) {\n          jest.advanceTimersByTime(0)\n        }\n      })\n\n      return result\n    } finally {\n      setReactActEnvironment(previousActEnvironment)\n    }\n  },\n  eventWrapper: cb => {\n    let result\n    act(() => {\n      result = cb()\n    })\n    return result\n  },\n})\n\n// Ideally we'd just use a WeakMap where containers are keys and roots are values.\n// We use two variables so that we can bail out in constant time when we render with a new container (most common use case)\n/**\n * @type {Set<import('react-dom').Container>}\n */\nconst mountedContainers = new Set()\n/**\n * @type Array<{container: import('react-dom').Container, root: ReturnType<typeof createConcurrentRoot>}>\n */\nconst mountedRootEntries = []\n\nfunction strictModeIfNeeded(innerElement, reactStrictMode) {\n  return reactStrictMode ?? getConfig().reactStrictMode\n    ? React.createElement(React.StrictMode, null, innerElement)\n    : innerElement\n}\n\nfunction wrapUiIfNeeded(innerElement, wrapperComponent) {\n  return wrapperComponent\n    ? React.createElement(wrapperComponent, null, innerElement)\n    : innerElement\n}\n\nfunction createConcurrentRoot(\n  container,\n  {\n    hydrate,\n    onCaughtError,\n    onRecoverableError,\n    ui,\n    wrapper: WrapperComponent,\n    reactStrictMode,\n  },\n) {\n  let root\n  if (hydrate) {\n    act(() => {\n      root = ReactDOMClient.hydrateRoot(\n        container,\n        strictModeIfNeeded(\n          wrapUiIfNeeded(ui, WrapperComponent),\n          reactStrictMode,\n        ),\n        {onCaughtError, onRecoverableError},\n      )\n    })\n  } else {\n    root = ReactDOMClient.createRoot(container, {\n      onCaughtError,\n      onRecoverableError,\n    })\n  }\n\n  return {\n    hydrate() {\n      /* istanbul ignore if */\n      if (!hydrate) {\n        throw new Error(\n          'Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.',\n        )\n      }\n      // Nothing to do since hydration happens when creating the root object.\n    },\n    render(element) {\n      root.render(element)\n    },\n    unmount() {\n      root.unmount()\n    },\n  }\n}\n\nfunction createLegacyRoot(container) {\n  return {\n    hydrate(element) {\n      ReactDOM.hydrate(element, container)\n    },\n    render(element) {\n      ReactDOM.render(element, container)\n    },\n    unmount() {\n      ReactDOM.unmountComponentAtNode(container)\n    },\n  }\n}\n\nfunction renderRoot(\n  ui,\n  {\n    baseElement,\n    container,\n    hydrate,\n    queries,\n    root,\n    wrapper: WrapperComponent,\n    reactStrictMode,\n  },\n) {\n  act(() => {\n    if (hydrate) {\n      root.hydrate(\n        strictModeIfNeeded(\n          wrapUiIfNeeded(ui, WrapperComponent),\n          reactStrictMode,\n        ),\n        container,\n      )\n    } else {\n      root.render(\n        strictModeIfNeeded(\n          wrapUiIfNeeded(ui, WrapperComponent),\n          reactStrictMode,\n        ),\n        container,\n      )\n    }\n  })\n\n  return {\n    container,\n    baseElement,\n    debug: (el = baseElement, maxLength, options) =>\n      Array.isArray(el)\n        ? // eslint-disable-next-line no-console\n          el.forEach(e => console.log(prettyDOM(e, maxLength, options)))\n        : // eslint-disable-next-line no-console,\n          console.log(prettyDOM(el, maxLength, options)),\n    unmount: () => {\n      act(() => {\n        root.unmount()\n      })\n    },\n    rerender: rerenderUi => {\n      renderRoot(rerenderUi, {\n        container,\n        baseElement,\n        root,\n        wrapper: WrapperComponent,\n        reactStrictMode,\n      })\n      // Intentionally do not return anything to avoid unnecessarily complicating the API.\n      // folks can use all the same utilities we return in the first place that are bound to the container\n    },\n    asFragment: () => {\n      /* istanbul ignore else (old jsdom limitation) */\n      if (typeof document.createRange === 'function') {\n        return document\n          .createRange()\n          .createContextualFragment(container.innerHTML)\n      } else {\n        const template = document.createElement('template')\n        template.innerHTML = container.innerHTML\n        return template.content\n      }\n    },\n    ...getQueriesForElement(baseElement, queries),\n  }\n}\n\nfunction render(\n  ui,\n  {\n    container,\n    baseElement = container,\n    legacyRoot = false,\n    onCaughtError,\n    onUncaughtError,\n    onRecoverableError,\n    queries,\n    hydrate = false,\n    wrapper,\n    reactStrictMode,\n  } = {},\n) {\n  if (onUncaughtError !== undefined) {\n    throw new Error(\n      'onUncaughtError is not supported. The `render` call will already throw on uncaught errors.',\n    )\n  }\n  if (legacyRoot && typeof ReactDOM.render !== 'function') {\n    const error = new Error(\n      '`legacyRoot: true` is not supported in this version of React. ' +\n        'If your app runs React 19 or later, you should remove this flag. ' +\n        'If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.',\n    )\n    Error.captureStackTrace(error, render)\n    throw error\n  }\n\n  if (!baseElement) {\n    // default to document.body instead of documentElement to avoid output of potentially-large\n    // head elements (such as JSS style blocks) in debug output\n    baseElement = document.body\n  }\n  if (!container) {\n    container = baseElement.appendChild(document.createElement('div'))\n  }\n\n  let root\n  // eslint-disable-next-line no-negated-condition -- we want to map the evolution of this over time. The root is created first. Only later is it re-used so we don't want to read the case that happens later first.\n  if (!mountedContainers.has(container)) {\n    const createRootImpl = legacyRoot ? createLegacyRoot : createConcurrentRoot\n    root = createRootImpl(container, {\n      hydrate,\n      onCaughtError,\n      onRecoverableError,\n      ui,\n      wrapper,\n      reactStrictMode,\n    })\n\n    mountedRootEntries.push({container, root})\n    // we'll add it to the mounted containers regardless of whether it's actually\n    // added to document.body so the cleanup method works regardless of whether\n    // they're passing us a custom container or not.\n    mountedContainers.add(container)\n  } else {\n    mountedRootEntries.forEach(rootEntry => {\n      // Else is unreachable since `mountedContainers` has the `container`.\n      // Only reachable if one would accidentally add the container to `mountedContainers` but not the root to `mountedRootEntries`\n      /* istanbul ignore else */\n      if (rootEntry.container === container) {\n        root = rootEntry.root\n      }\n    })\n  }\n\n  return renderRoot(ui, {\n    container,\n    baseElement,\n    queries,\n    hydrate,\n    wrapper,\n    root,\n    reactStrictMode,\n  })\n}\n\nfunction cleanup() {\n  mountedRootEntries.forEach(({root, container}) => {\n    act(() => {\n      root.unmount()\n    })\n    if (container.parentNode === document.body) {\n      document.body.removeChild(container)\n    }\n  })\n  mountedRootEntries.length = 0\n  mountedContainers.clear()\n}\n\nfunction renderHook(renderCallback, options = {}) {\n  const {initialProps, ...renderOptions} = options\n\n  if (renderOptions.legacyRoot && typeof ReactDOM.render !== 'function') {\n    const error = new Error(\n      '`legacyRoot: true` is not supported in this version of React. ' +\n        'If your app runs React 19 or later, you should remove this flag. ' +\n        'If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.',\n    )\n    Error.captureStackTrace(error, renderHook)\n    throw error\n  }\n\n  const result = React.createRef()\n\n  function TestComponent({renderCallbackProps}) {\n    const pendingResult = renderCallback(renderCallbackProps)\n\n    React.useEffect(() => {\n      result.current = pendingResult\n    })\n\n    return null\n  }\n\n  const {rerender: baseRerender, unmount} = render(\n    <TestComponent renderCallbackProps={initialProps} />,\n    renderOptions,\n  )\n\n  function rerender(rerenderCallbackProps) {\n    return baseRerender(\n      <TestComponent renderCallbackProps={rerenderCallbackProps} />,\n    )\n  }\n\n  return {result, rerender, unmount}\n}\n\n// just re-export everything from dom-testing-library\nexport * from '@testing-library/dom'\nexport {render, renderHook, cleanup, act, fireEvent, getConfig, configure}\n\n/* eslint func-name-matching:0 */\n"], "names": ["reactAct", "React", "act", "DeprecatedReactTestUtils", "getGlobalThis", "globalThis", "self", "window", "global", "Error", "setIsReactActEnvironment", "isReactActEnvironment", "IS_REACT_ACT_ENVIRONMENT", "getIsReactActEnvironment", "actImplementation", "callback", "previousActEnvironment", "callbackNeedsToBeAwaited", "actResult", "result", "then", "resolve", "reject", "returnValue", "error", "fireEvent", "dtlFireEvent", "arguments", "Object", "keys", "for<PERSON>ach", "key", "mouseEnter", "mouseLeave", "mouseOver", "mouseOut", "pointerEnter", "pointerLeave", "pointerOver", "pointerOut", "select", "node", "init", "focus", "keyUp", "blur", "focusOut", "focusIn", "configForRTL", "reactStrictMode", "getConfig", "getConfigDTL", "configureDTL", "unstable_advanceTimersWrapper", "cb", "asyncWrapper", "async", "setReactActEnvironment", "Promise", "setTimeout", "jest", "_isMockFunction", "prototype", "hasOwnProperty", "call", "advanceTimersByTime", "eventWrapper", "mountedContainers", "Set", "mountedRootEntries", "strictModeIfNeeded", "innerElement", "createElement", "StrictMode", "wrapUiIfNeeded", "wrapperComponent", "createConcurrentRoot", "container", "_ref", "root", "hydrate", "onCaughtError", "onRecoverableError", "ui", "wrapper", "WrapperComponent", "ReactDOMClient", "hydrateRoot", "createRoot", "render", "element", "unmount", "createLegacyRoot", "ReactDOM", "unmountComponentAtNode", "renderRoot", "_ref2", "baseElement", "queries", "debug", "el", "max<PERSON><PERSON><PERSON>", "options", "Array", "isArray", "e", "console", "log", "prettyDOM", "rerender", "rerenderUi", "asFragment", "document", "createRange", "createContextualFragment", "innerHTML", "template", "content", "getQueriesForElement", "_temp", "legacyRoot", "onUncaughtError", "undefined", "captureStackTrace", "body", "append<PERSON><PERSON><PERSON>", "has", "rootEntry", "push", "add", "_ref3", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "length", "clear", "newConfig", "configForDTL", "configure", "renderHook", "renderCallback", "initialProps", "renderOptions", "createRef", "TestComponent", "_ref4", "renderCallbackProps", "pendingResult", "useEffect", "current", "baseRerender", "rerenderCallbackProps"], "mappings": "85BAGA,MAAMA,EACiB,mBAAdC,EAAMC,IAAqBD,EAAMC,IAAMC,EAAyBD,IAEzE,SAASE,IAEP,GAA0B,oBAAfC,WACT,OAAOA,WAGT,GAAoB,oBAATC,KACT,OAAOA,KAGT,GAAsB,oBAAXC,OACT,OAAOA,OAGT,GAAsB,oBAAXC,OACT,OAAOA,OAGT,MAAM,IAAIC,MAAM,iCAClB,CAEA,SAASC,EAAyBC,GAChCP,IAAgBQ,yBAA2BD,CAC7C,CAEA,SAASE,IACP,OAAOT,IAAgBQ,wBACzB,CAiDA,MAAMV,GA/C4BY,EA+CGd,EA9C5Be,IACL,MAAMC,EAAyBH,IAC/BH,GAAyB,GACzB,IAEE,IAAIO,GAA2B,EAC/B,MAAMC,EAAYJ,GAAkB,KAClC,MAAMK,EAASJ,IAQf,OANa,OAAXI,GACkB,iBAAXA,GACgB,mBAAhBA,EAAOC,OAEdH,GAA2B,GAEtBE,CAAM,IAEf,OAAIF,EAEK,CACLG,KAAMA,CAACC,EAASC,KAFDJ,EAGJE,MACPG,IACEb,EAAyBM,GACzBK,EAAQE,EAAY,IAEtBC,IACEd,EAAyBM,GACzBM,EAAOE,EAAM,GAEhB,IAILd,EAAyBM,GAClBE,EAEV,CAAC,MAAOM,GAIP,MADAd,EAAyBM,GACnBQ,CACR,IA3CJ,IAAkCV,EC7B5BW,MAAAA,EAAY,WAAA,OAAaC,EAAYD,aAACE,UAAQ,EAEpDC,OAAOC,KAAKH,EAAAA,WAAcI,SAAQC,IAChCN,EAAUM,GAAO,WAAA,OAAaL,YAAaK,MAAKJ,UAAQ,CAAA,IAM1D,MAAMK,EAAaP,EAAUO,WACvBC,EAAaR,EAAUQ,WAC7BR,EAAUO,WAAa,WAErB,OADAA,KAAWL,WACJF,EAAUS,aAAUP,UAC7B,EACAF,EAAUQ,WAAa,WAErB,OADAA,KAAWN,WACJF,EAAUU,YAASR,UAC5B,EAEA,MAAMS,EAAeX,EAAUW,aACzBC,EAAeZ,EAAUY,aAC/BZ,EAAUW,aAAe,WAEvB,OADAA,KAAaT,WACNF,EAAUa,eAAYX,UAC/B,EACAF,EAAUY,aAAe,WAEvB,OADAA,KAAaV,WACNF,EAAUc,cAAWZ,UAC9B,EAEA,MAAMa,EAASf,EAAUe,OACzBf,EAAUe,OAAS,CAACC,EAAMC,KACxBF,EAAOC,EAAMC,GAEbD,EAAKE,QAULlB,EAAUmB,MAAMH,EAAMC,EAAK,EAM7B,MAAMG,EAAOpB,EAAUoB,KACjBF,EAAQlB,EAAUkB,MACxBlB,EAAUoB,KAAO,WAEf,OADApB,EAAUqB,YAASnB,WACZkB,KAAKlB,UACd,EACAF,EAAUkB,MAAQ,WAEhB,OADAlB,EAAUsB,WAAQpB,WACXgB,KAAMhB,UACf,EC7DA,IAAIqB,EAAe,CACjBC,iBAAiB,GAGnB,SAASC,IACP,MAAO,IACFC,iBACAH,EAEP,CCeAI,EAAAA,UAAa,CACXC,8BAA+BC,GACtBpD,EAAIoD,GAKbC,aAAcC,UACZ,MAAMxC,EAAyBH,IAC/B4C,GAAuB,GACvB,IACE,MAAMtC,QAAemC,IAcrB,aAVM,IAAII,SAAQrC,IAChBsC,YAAW,KACTtC,GAAS,GACR,GA9BW,oBAATuC,MAAiC,OAATA,OAGA,IAA/BD,WAAWE,kBAEXjC,OAAOkC,UAAUC,eAAeC,KAAKL,WAAY,UA4B7CC,KAAKK,oBAAoB,EAC3B,IAGK9C,CACT,CAAU,QACRsC,EAAuBzC,EACzB,GAEFkD,aAAcZ,IACZ,IAAInC,EAIJ,OAHAjB,GAAI,KACFiB,EAASmC,GAAI,IAERnC,CAAM,IASjB,MAAMgD,EAAoB,IAAIC,IAIxBC,EAAqB,GAE3B,SAASC,EAAmBC,EAActB,GACxC,OAAOA,GAAmBC,IAAYD,gBAClChD,EAAMuE,cAAcvE,EAAMwE,WAAY,KAAMF,GAC5CA,CACN,CAEA,SAASG,EAAeH,EAAcI,GACpC,OAAOA,EACH1E,EAAMuE,cAAcG,EAAkB,KAAMJ,GAC5CA,CACN,CAEA,SAASK,EACPC,EAASC,GAST,IACIC,GATJC,QACEA,EAAOC,cACPA,EAAaC,mBACbA,EAAkBC,GAClBA,EACAC,QAASC,EAAgBpC,gBACzBA,GACD6B,EAqBD,OAlBIE,EACF9E,GAAI,KACF6E,EAAOO,EAAeC,YACpBV,EACAP,EACEI,EAAeS,EAAIE,GACnBpC,GAEF,CAACgC,gBAAeC,sBACjB,IAGHH,EAAOO,EAAeE,WAAWX,EAAW,CAC1CI,gBACAC,uBAIG,CACLF,OAAAA,GAEE,IAAKA,EACH,MAAM,IAAIvE,MACR,0FAIL,EACDgF,MAAAA,CAAOC,GACLX,EAAKU,OAAOC,EACb,EACDC,OAAAA,GACEZ,EAAKY,SACP,EAEJ,CAEA,SAASC,EAAiBf,GACxB,MAAO,CACLG,OAAAA,CAAQU,GACNG,EAAAA,QAASb,QAAQU,EAASb,EAC3B,EACDY,MAAAA,CAAOC,GACLG,EAAAA,QAASJ,OAAOC,EAASb,EAC1B,EACDc,OAAAA,GACEE,UAASC,uBAAuBjB,EAClC,EAEJ,CAEA,SAASkB,EACPZ,EAAEa,GAUF,IATAC,YACEA,EAAWpB,UACXA,EAASG,QACTA,EAAOkB,QACPA,EAAOnB,KACPA,EACAK,QAASC,EAAgBpC,gBACzBA,GACD+C,EAsBD,OApBA9F,GAAI,KACE8E,EACFD,EAAKC,QACHV,EACEI,EAAeS,EAAIE,GACnBpC,GAEF4B,GAGFE,EAAKU,OACHnB,EACEI,EAAeS,EAAIE,GACnBpC,GAEF4B,EAEJ,IAGK,CACLA,YACAoB,cACAE,MAAO,SAACC,EAAkBC,EAAWC,GAAb,YAAd,IAAFF,IAAAA,EAAKH,GACXM,MAAMC,QAAQJ,GAEVA,EAAGtE,SAAQ2E,GAAKC,QAAQC,IAAIC,EAASA,UAACH,EAAGJ,EAAWC,MAEpDI,QAAQC,IAAIC,EAASA,UAACR,EAAIC,EAAWC,GAAS,EACpDX,QAASA,KACPzF,GAAI,KACF6E,EAAKY,SAAS,GACd,EAEJkB,SAAUC,IACRf,EAAWe,EAAY,CACrBjC,YACAoB,cACAlB,OACAK,QAASC,EACTpC,mBACA,EAIJ8D,WAAYA,KAEV,GAAoC,mBAAzBC,SAASC,YAClB,OAAOD,SACJC,cACAC,yBAAyBrC,EAAUsC,WACjC,CACL,MAAMC,EAAWJ,SAASxC,cAAc,YAExC,OADA4C,EAASD,UAAYtC,EAAUsC,UACxBC,EAASC,OAClB,MAECC,EAAoBA,qBAACrB,EAAaC,GAEzC,CAEA,SAAST,EACPN,EAAEoC,GAaF,IAyBIxC,GArCJF,UACEA,EAASoB,YACTA,EAAcpB,EAAS2C,WACvBA,GAAa,EAAKvC,cAClBA,EAAawC,gBACbA,EAAevC,mBACfA,EAAkBgB,QAClBA,EAAOlB,QACPA,GAAU,EAAKI,QACfA,EAAOnC,gBACPA,QACD,IAAAsE,EAAG,CAAA,EAAEA,EAEN,QAAwBG,IAApBD,EACF,MAAM,IAAIhH,MACR,8FAGJ,GAAI+G,GAAyC,mBAApB3B,UAASJ,OAAuB,CACvD,MAAMjE,EAAQ,IAAIf,MAChB,iQAKF,MADAA,MAAMkH,kBAAkBnG,EAAOiE,GACzBjE,CACR,CAaA,GAXKyE,IAGHA,EAAce,SAASY,MAEpB/C,IACHA,EAAYoB,EAAY4B,YAAYb,SAASxC,cAAc,SAKxDL,EAAkB2D,IAAIjD,GAiBzBR,EAAmBvC,SAAQiG,IAIrBA,EAAUlD,YAAcA,IAC1BE,EAAOgD,EAAUhD,KACnB,QAvBmC,CAErCA,GADuByC,EAAa5B,EAAmBhB,GACjCC,EAAW,CAC/BG,UACAC,gBACAC,qBACAC,KACAC,UACAnC,oBAGFoB,EAAmB2D,KAAK,CAACnD,YAAWE,SAIpCZ,EAAkB8D,IAAIpD,EACxB,CAWA,OAAOkB,EAAWZ,EAAI,CACpBN,YACAoB,cACAC,UACAlB,UACAI,UACAL,OACA9B,mBAEJ,mBAEA,WACEoB,EAAmBvC,SAAQoG,IAAuB,IAAtBnD,KAACA,EAAIF,UAAEA,GAAUqD,EAC3ChI,GAAI,KACF6E,EAAKY,SAAS,IAEZd,EAAUsD,aAAenB,SAASY,MACpCZ,SAASY,KAAKQ,YAAYvD,EAC5B,IAEFR,EAAmBgE,OAAS,EAC5BlE,EAAkBmE,OACpB,cD7SA,SAAmBC,GACQ,mBAAdA,IAGTA,EAAYA,EAAUrF,MAGxB,MAAMD,gBAACA,KAAoBuF,GAAgBD,EAE3CnF,EAAYqF,UAACD,GAEbxF,EAAe,IACVA,EACHC,kBAEJ,sDCgSA,SAASyF,EAAWC,EAAgBrC,QAAO,IAAPA,IAAAA,EAAU,CAAA,GAC5C,MAAMsC,aAACA,KAAiBC,GAAiBvC,EAEzC,GAAIuC,EAAcrB,YAAyC,mBAApB3B,EAAAA,QAASJ,OAAuB,CACrE,MAAMjE,EAAQ,IAAIf,MAChB,iQAKF,MADAA,MAAMkH,kBAAkBnG,EAAOkH,GACzBlH,CACR,CAEA,MAAML,EAASlB,EAAM6I,YAErB,SAASC,EAAaC,GAAwB,IAAvBC,oBAACA,GAAoBD,EAC1C,MAAME,EAAgBP,EAAeM,GAMrC,OAJAhJ,EAAMkJ,WAAU,KACdhI,EAAOiI,QAAUF,CAAa,IAGzB,IACT,CAEA,MAAOrC,SAAUwC,EAAY1D,QAAEA,GAAWF,EACxCxF,EAAAuE,cAACuE,EAAa,CAACE,oBAAqBL,IACpCC,GASF,MAAO,CAAC1H,SAAQ0F,SANhB,SAAkByC,GAChB,OAAOD,EACLpJ,EAAAuE,cAACuE,EAAa,CAACE,oBAAqBK,IAIhB,EAAE3D,UAC5B"}