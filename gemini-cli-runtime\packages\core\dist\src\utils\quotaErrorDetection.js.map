{"version": 3, "file": "quotaErrorDetection.js", "sourceRoot": "", "sources": ["../../../src/utils/quotaErrorDetection.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAgBH,MAAM,UAAU,UAAU,CAAC,KAAc;IACvC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,OAAO,IAAI,KAAK;QAChB,OAAQ,KAAkB,CAAC,KAAK,KAAK,QAAQ;QAC7C,SAAS,IAAK,KAAkB,CAAC,KAAK,CACvC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,KAAc;IAC9C,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,SAAS,IAAI,KAAK;QAClB,OAAQ,KAAyB,CAAC,OAAO,KAAK,QAAQ,CACvD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,KAAc;IACpD,0EAA0E;IAC1E,iCAAiC;IACjC,gEAAgE;IAChE,wEAAwE;IACxE,wEAAwE;IAExE,MAAM,YAAY,GAAG,CAAC,OAAe,EAAW,EAAE;QAChD,OAAO,CAAC,GAAG,CAAC,mDAAmD,EAAE,OAAO,CAAC,CAAC;QAC1E,MAAM,MAAM,GACV,OAAO,CAAC,QAAQ,CAAC,yCAAyC,CAAC;YAC3D,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,MAAM,CAAC,CAAC;QAC/D,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,wDAAwD;IACxD,OAAO,CAAC,GAAG,CACT,sDAAsD,EACtD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAC/B,CAAC;IAEF,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,kDAAkD;IAClD,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;QAC9D,MAAM,WAAW,GAAG,KAInB,CAAC;QACF,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtD,OAAO,CAAC,GAAG,CACT,2DAA2D,EAC3D,WAAW,CAAC,QAAQ,CAAC,IAAI,CAC1B,CAAC;YACF,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAClD,OAAO,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC;YACD,IACE,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ;gBAC7C,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI;gBAClC,OAAO,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,EACpC,CAAC;gBACD,MAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,IAEtC,CAAC;gBACF,OAAO,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAAG,CACT,iEAAiE,EACjE,KAAK,CACN,CAAC;IACF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,2BAA2B,CAAC,KAAc;IACxD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC,QAAQ,CAAC,iCAAiC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,CAAC;IACnE,CAAC;IAED,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,CAAC;IACzE,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}