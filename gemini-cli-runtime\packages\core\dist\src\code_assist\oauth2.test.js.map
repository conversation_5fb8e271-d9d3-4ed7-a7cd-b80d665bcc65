{"version": 3, "file": "oauth2.test.js", "sourceRoot": "", "sources": ["../../../src/code_assist/oauth2.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAQ,MAAM,QAAQ,CAAC;AAC/E,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,EAAE,QAAQ,EAAE,MAAM,6BAA6B,CAAC;AAEvD,OAAO,QAAQ,MAAM,eAAe,CAAC;AAErC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;IACrC,MAAM,EAAE,GAAG,MAAM,cAAc,EAAuB,CAAC;IACvD,OAAO;QACL,GAAG,EAAE;QACL,OAAO,EAAE,EAAE,CAAC,EAAE,EAAE;KACjB,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAC/B,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChB,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClB,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAEzB,MAAM,UAAU,GAAG;IACjB,YAAY,EAAE,GAAG,EAAE,CAAC,KAAK;CACL,CAAC;AAEvB,sBAAsB;AACtB,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AAEvB,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,IAAI,WAAmB,CAAC;IAExB,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,EAAE,CAAC,WAAW,CAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,uBAAuB,CAAC,CAChD,CAAC;QACD,EAAE,CAAC,OAAgB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IACH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACzD,EAAE,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QAC1C,MAAM,WAAW,GAAG,0BAA0B,CAAC;QAC/C,MAAM,QAAQ,GAAG,WAAW,CAAC;QAC7B,MAAM,SAAS,GAAG,YAAY,CAAC;QAC/B,MAAM,UAAU,GAAG;YACjB,YAAY,EAAE,mBAAmB;YACjC,aAAa,EAAE,oBAAoB;SACpC,CAAC;QAEF,MAAM,mBAAmB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QACvE,MAAM,kBAAkB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,MAAM,kBAAkB,GAAG,EAAE;aAC1B,EAAE,EAAE;aACJ,iBAAiB,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;QACrD,MAAM,gBAAgB,GAAG;YACvB,eAAe,EAAE,mBAAmB;YACpC,QAAQ,EAAE,YAAY;YACtB,cAAc,EAAE,kBAAkB;YAClC,cAAc,EAAE,kBAAkB;YAClC,WAAW,EAAE,UAAU;YACvB,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;SACe,CAAC;QAC5B,YAAgC,CAAC,kBAAkB,CAClD,GAAG,EAAE,CAAC,gBAAgB,CACvB,CAAC;QAEF,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,eAAe,CAAC,SAAkB,CAAC,CAAC;QACnE,IAAa,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,CAAU,CAAC,CAAC;QAE7D,iCAAiC;QAChC,MAAM,CAAC,KAAc,CAAC,iBAAiB,CAAC;YACvC,EAAE,EAAE,IAAI;YACR,IAAI,EAAE,EAAE;iBACL,EAAE,EAAE;iBACJ,iBAAiB,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;SAC1C,CAAC,CAAC;QAE1B,IAAI,eAGH,CAAC;QAEF,IAAI,uBAAiD,CAAC;QACtD,MAAM,sBAAsB,GAAG,IAAI,OAAO,CACxC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,uBAAuB,GAAG,OAAO,CAAC,CACjD,CAAC;QAEF,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,IAAY,EAAE,QAAqB,EAAE,EAAE;gBACpD,YAAY,GAAG,IAAI,CAAC;gBACpB,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,EAAE,CAAC;gBACb,CAAC;gBACD,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC,CAAC;YACF,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,QAAqB,EAAE,EAAE;gBACrC,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,EAAE,CAAC;gBACb,CAAC;YACH,CAAC,CAAC;YACF,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;YACX,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;SACxC,CAAC;QACD,IAAI,CAAC,YAAqB,CAAC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE;YACpD,eAAe,GAAG,EAGjB,CAAC;YACF,OAAO,cAAwC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,cAAc,CAClC,QAAQ,CAAC,iBAAiB,EAC1B,UAAU,CACX,CAAC;QAEF,sCAAsC;QACtC,MAAM,sBAAsB,CAAC;QAE7B,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,wBAAwB,QAAQ,UAAU,SAAS,EAAE;SACnC,CAAC;QAC1B,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE;YAClB,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;SACqB,CAAC;QAEpC,MAAM,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAExC,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC;QACnC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEtC,MAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC/C,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC;YACxC,IAAI,EAAE,QAAQ;YACd,YAAY,EAAE,oBAAoB,YAAY,iBAAiB;SAChE,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAE5D,mCAAmC;QACnC,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CACjC,WAAW,EACX,SAAS,EACT,sBAAsB,CACvB,CAAC;QACF,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,mBAAmB,GAAG,EAAE,CAAC,YAAY,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,OAAO,CAAC;YAC9C,MAAM,EAAE,+BAA+B;YACvC,GAAG,EAAE,EAAE;SACR,CAAC,CAAC;QAEH,mDAAmD;QACnD,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,uBAAuB,GAAG;YAC9B,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI;SACJ,CAAC;QAEvB,MAAM,gBAAgB,GAAG;YACvB,aAAa,EAAE,gBAAgB;YAC/B,YAAY,EAAE,eAAe;SAC9B,CAAC;QACF,MAAM,WAAW,GAAG,oCAAoC,CAAC;QACzD,MAAM,QAAQ,GAAG,gBAAgB,CAAC;QAClC,MAAM,UAAU,GAAG;YACjB,YAAY,EAAE,6BAA6B;YAC3C,aAAa,EAAE,8BAA8B;SAC9C,CAAC;QAEF,MAAM,mBAAmB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QACvE,MAAM,kBAAkB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,MAAM,6BAA6B,GAAG,EAAE;aACrC,EAAE,EAAE;aACJ,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAEvC,MAAM,gBAAgB,GAAG;YACvB,eAAe,EAAE,mBAAmB;YACpC,QAAQ,EAAE,YAAY;YACtB,cAAc,EAAE,kBAAkB;YAClC,yBAAyB,EAAE,6BAA6B;YACxD,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;SACe,CAAC;QAC5B,YAAgC,CAAC,kBAAkB,CAClD,GAAG,EAAE,CAAC,gBAAgB,CACvB,CAAC;QAEF,MAAM,YAAY,GAAG;YACnB,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzD,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE;SACf,CAAC;QACD,QAAQ,CAAC,eAAwB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEjE,MAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAE5E,MAAM,MAAM,GAAG,MAAM,cAAc,CACjC,QAAQ,CAAC,iBAAiB,EAC1B,uBAAuB,CACxB,CAAC;QAEF,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEtC,uBAAuB;QACvB,MAAM,CAAC,6BAA6B,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACzD,MAAM,CAAC,mBAAmB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC/C,MAAM,CAAC,aAAa,CAAC,CAAC,oBAAoB,CACxC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CACrC,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAChD,gCAAgC,EAChC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CACrB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC;YACxC,IAAI,EAAE,QAAQ;YACd,YAAY,EAAE,gBAAgB,CAAC,YAAY;YAC3C,YAAY,EAAE,sDAAsD;SACrE,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAE5D,aAAa,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,MAAM,kBAAkB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,IAAI,iBAA0B,CAAC;QAE/B,UAAU,CAAC,GAAG,EAAE;YACd,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YACtD,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC5D,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAChE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,iBAAiB,CACjD,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAC5B,CAAC,CAAC,6BAA6B;YAEhC,kBAAkB,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;YACrE,iBAAiB,GAAG;gBAClB,WAAW,EAAE,EAAE,aAAa,EAAE,oBAAoB,EAAE;gBACpD,cAAc,EAAE,kBAAkB;aACb,CAAC;YAEvB,OAA2B,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,WAAW,GAAG,EAAE,aAAa,EAAE,cAAc,EAAE,CAAC;YACtD,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,iBAAiB,CACjD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAC5B,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE;gBACvB,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;gBAClE,YAAY,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC3C,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;aACZ,CAAC;YAEF,qDAAqD;YACpD,YAAgC,CAAC,kBAAkB,CAClD,GAAG,EAAE,CAAC,UAAqC,CAC5C,CAAC;YAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YAE7D,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAC/C,qCAAqC,EACrC,OAAO,CACR,CAAC;YACF,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,gDAAgD;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAEvD,MAAM,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,cAAc,GAAG,EAAE,aAAa,EAAE,eAAe,EAAE,CAAC;YAC1D,iBAAiB,CAAC,WAAW,GAAG,cAAc,CAAC;YAC/C,kBAAkB,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,MAAM,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAEvD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YACtE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;YAC1C,kBAAkB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhD,MAAM,MAAM,CACV,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CACjD,CAAC,OAAO,CAAC,OAAO,CACf,iLAAiL,CAClL,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}