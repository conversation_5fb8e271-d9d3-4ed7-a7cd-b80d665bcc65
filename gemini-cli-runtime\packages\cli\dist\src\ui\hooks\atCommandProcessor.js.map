{"version": 3, "file": "atCommandProcessor.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/atCommandProcessor.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B,OAAO,EAEL,eAAe,EACf,WAAW,EACX,YAAY,GACb,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAGL,cAAc,GACf,MAAM,aAAa,CAAC;AAsBrB;;;GAGG;AACH,SAAS,kBAAkB,CAAC,KAAa;IACvC,MAAM,KAAK,GAAoB,EAAE,CAAC;IAClC,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,OAAO,YAAY,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QACnC,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;QACjB,IAAI,eAAe,GAAG,YAAY,CAAC;QACnC,0BAA0B;QAC1B,OAAO,eAAe,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YACtC,IACE,KAAK,CAAC,eAAe,CAAC,KAAK,GAAG;gBAC9B,CAAC,eAAe,KAAK,CAAC,IAAI,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,EAC9D,CAAC;gBACD,OAAO,GAAG,eAAe,CAAC;gBAC1B,MAAM;YACR,CAAC;YACD,eAAe,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC;YACnB,YAAY;YACZ,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAChC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACvE,CAAC;YACD,MAAM;QACR,CAAC;QAED,oBAAoB;QACpB,IAAI,OAAO,GAAG,YAAY,EAAE,CAAC;YAC3B,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC;aAChD,CAAC,CAAC;QACL,CAAC;QAED,cAAc;QACd,IAAI,YAAY,GAAG,OAAO,GAAG,CAAC,CAAC;QAC/B,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,OAAO,YAAY,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBACb,QAAQ,GAAG,KAAK,CAAC;YACnB,CAAC;iBAAM,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBACzB,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3B,4CAA4C;gBAC5C,MAAM;YACR,CAAC;YACD,YAAY,EAAE,CAAC;QACjB,CAAC;QACD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACzD,uEAAuE;QACvE,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;QACvC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QAChD,YAAY,GAAG,YAAY,CAAC;IAC9B,CAAC;IACD,mGAAmG;IACnG,OAAO,KAAK,CAAC,MAAM,CACjB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAChE,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe,CAAC,EACpC,KAAK,EACL,MAAM,EACN,OAAO,EACP,cAAc,EACd,SAAS,EAAE,oBAAoB,EAC/B,MAAM,GACgB;IACtB,MAAM,YAAY,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC/C,MAAM,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAC5C,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CACjC,CAAC;IAEF,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACpC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,oBAAoB,CAAC,CAAC;QAC7D,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;IACpE,CAAC;IAED,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAE7D,yCAAyC;IACzC,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;IAC9C,MAAM,gBAAgB,GAAG,MAAM,CAAC,gCAAgC,EAAE,CAAC;IAEnE,MAAM,eAAe,GAAa,EAAE,CAAC;IACrC,MAAM,uBAAuB,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC1D,MAAM,uBAAuB,GAAa,EAAE,CAAC;IAC7C,MAAM,YAAY,GAAa,EAAE,CAAC;IAElC,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,eAAe,EAAE,CAAC;IACpD,MAAM,iBAAiB,GAAG,YAAY,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAClE,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,OAAO,CACL,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,wCAAwC,EAAE,EACjE,oBAAoB,CACrB,CAAC;QACF,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IACxD,CAAC;IAED,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,2BAA2B;QAEtE,IAAI,cAAc,KAAK,GAAG,EAAE,CAAC;YAC3B,cAAc,CACZ,iEAAiE,CAClE,CAAC;YACF,SAAS;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,oFAAoF;YACpF,sBAAsB;YACtB,OAAO,CACL;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,6BAA6B,cAAc,uBAAuB;aACzE,EACD,oBAAoB,CACrB,CAAC;YACF,iFAAiF;YACjF,2EAA2E;YAC3E,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;QACxD,CAAC;QAED,6DAA6D;QAC7D,IAAI,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;YACnE,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC;YACnE,cAAc,CAAC,QAAQ,QAAQ,OAAO,MAAM,uBAAuB,CAAC,CAAC;YACrE,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5B,SAAS;QACX,CAAC;QAED,IAAI,eAAe,GAAG,QAAQ,CAAC;QAC/B,IAAI,oBAAoB,GAAG,KAAK,CAAC;QAEjC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,QAAQ,CAAC,CAAC;YACnE,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1C,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACtC,CAAC,CAAC,GAAG,QAAQ,IAAI;oBACjB,CAAC,CAAC,GAAG,QAAQ,KAAK,CAAC;gBACrB,cAAc,CACZ,QAAQ,QAAQ,uCAAuC,eAAe,EAAE,CACzE,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,cAAc,CAAC,QAAQ,QAAQ,sBAAsB,eAAe,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,oBAAoB,GAAG,IAAI,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAClD,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,QAAQ,EAAE,CAAC;oBACtD,cAAc,CACZ,QAAQ,QAAQ,8CAA8C,CAC/D,CAAC;oBACF,IAAI,CAAC;wBACH,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,OAAO,CACvC,EAAE,OAAO,EAAE,OAAO,QAAQ,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,YAAY,EAAE,EAAE,EAC5D,MAAM,CACP,CAAC;wBACF,IACE,UAAU,CAAC,UAAU;4BACrB,OAAO,UAAU,CAAC,UAAU,KAAK,QAAQ;4BACzC,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,CAAC;4BACnD,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC3C,CAAC;4BACD,MAAM,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAChD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gCACjC,MAAM,kBAAkB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gCAC3C,eAAe,GAAG,IAAI,CAAC,QAAQ,CAC7B,MAAM,CAAC,YAAY,EAAE,EACrB,kBAAkB,CACnB,CAAC;gCACF,cAAc,CACZ,mBAAmB,QAAQ,UAAU,kBAAkB,0BAA0B,eAAe,EAAE,CACnG,CAAC;gCACF,oBAAoB,GAAG,IAAI,CAAC;4BAC9B,CAAC;iCAAM,CAAC;gCACN,cAAc,CACZ,wBAAwB,QAAQ,yCAAyC,QAAQ,mBAAmB,CACrG,CAAC;4BACJ,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,cAAc,CACZ,wBAAwB,QAAQ,uCAAuC,QAAQ,mBAAmB,CACnG,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAAC,OAAO,SAAS,EAAE,CAAC;wBACnB,OAAO,CAAC,KAAK,CACX,gCAAgC,QAAQ,KAAK,eAAe,CAAC,SAAS,CAAC,EAAE,CAC1E,CAAC;wBACF,cAAc,CACZ,gCAAgC,QAAQ,UAAU,QAAQ,mBAAmB,CAC9E,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,cAAc,CACZ,6BAA6B,QAAQ,mBAAmB,CACzD,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CACX,sBAAsB,QAAQ,KAAK,eAAe,CAAC,KAAK,CAAC,EAAE,CAC5D,CAAC;gBACF,cAAc,CACZ,sBAAsB,QAAQ,UAAU,QAAQ,mBAAmB,CACpE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,oBAAoB,EAAE,CAAC;YACzB,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,uBAAuB,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;YAC7D,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,sDAAsD;IACtD,IAAI,gBAAgB,GAAG,EAAE,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,oBAAoB;YACpB,MAAM,YAAY,GAAG,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/D,IACE,CAAC,GAAG,CAAC;gBACL,gBAAgB,CAAC,MAAM,GAAG,CAAC;gBAC3B,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAC/B,YAAY,EACZ,CAAC;gBACD,0FAA0F;gBAC1F,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACrC,IACE,QAAQ,CAAC,IAAI,KAAK,MAAM;oBACxB,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ;wBACzB,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAChD,CAAC;oBACD,gBAAgB,IAAI,GAAG,CAAC;gBAC1B,CAAC;YACH,CAAC;YACD,IAAI,YAAY,EAAE,CAAC;gBACjB,gBAAgB,IAAI,IAAI,YAAY,EAAE,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,8EAA8E;gBAC9E,kFAAkF;gBAClF,IACE,CAAC,GAAG,CAAC;oBACL,gBAAgB,CAAC,MAAM,GAAG,CAAC;oBAC3B,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAC/B,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAC7B,CAAC;oBACD,gBAAgB,IAAI,GAAG,CAAC;gBAC1B,CAAC;gBACD,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IACD,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,EAAE,CAAC;IAE3C,kCAAkC;IAClC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5B,MAAM,UAAU,GAAG,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACvE,cAAc,CACZ,WAAW,YAAY,CAAC,MAAM,IAAI,UAAU,WAAW,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACjF,CAAC;IACJ,CAAC;IAED,6FAA6F;IAC7F,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjC,cAAc,CAAC,kDAAkD,CAAC,CAAC;QACnE,IAAI,gBAAgB,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;YACrD,gFAAgF;YAChF,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;QACpE,CAAC;aAAM,IAAI,CAAC,gBAAgB,IAAI,KAAK,EAAE,CAAC;YACtC,8EAA8E;YAC9E,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;QACpE,CAAC;QACD,kGAAkG;QAClG,OAAO;YACL,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,gBAAgB,IAAI,KAAK,EAAE,CAAC;YACrD,aAAa,EAAE,IAAI;SACpB,CAAC;IACJ,CAAC;IAED,MAAM,mBAAmB,GAAgB,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAEtE,MAAM,QAAQ,GAAG;QACf,KAAK,EAAE,eAAe;QACtB,kBAAkB,EAAE,gBAAgB,EAAE,4BAA4B;KACnE,CAAC;IACF,IAAI,eAA0C,CAAC;IAE/C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACjE,eAAe,GAAG;YAChB,MAAM,EAAE,eAAe,oBAAoB,EAAE;YAC7C,IAAI,EAAE,iBAAiB,CAAC,WAAW;YACnC,WAAW,EAAE,iBAAiB,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,MAAM,EAAE,cAAc,CAAC,OAAO;YAC9B,aAAa,EACX,MAAM,CAAC,aAAa;gBACpB,sBAAsB,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5D,mBAAmB,EAAE,SAAS;SAC/B,CAAC;QAEF,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACrC,MAAM,gBAAgB,GAAG,mCAAmC,CAAC;YAC7D,mBAAmB,CAAC,IAAI,CAAC;gBACvB,IAAI,EAAE,yCAAyC;aAChD,CAAC,CAAC;YACH,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACrC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC7B,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC1C,IAAI,KAAK,EAAE,CAAC;wBACV,MAAM,qBAAqB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;wBACtE,MAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBAC1C,mBAAmB,CAAC,IAAI,CAAC;4BACvB,IAAI,EAAE,mBAAmB,qBAAqB,KAAK;yBACpD,CAAC,CAAC;wBACH,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;oBACxD,CAAC;yBAAM,CAAC;wBACN,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,yBAAyB;oBACzB,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;YACD,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACN,cAAc,CACZ,4DAA4D,CAC7D,CAAC;QACJ,CAAC;QAED,OAAO,CACL,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,eAAe,CAAC,EAG7C,EACD,oBAAoB,CACrB,CAAC;QACF,OAAO,EAAE,cAAc,EAAE,mBAAmB,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;IACtE,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,eAAe,GAAG;YAChB,MAAM,EAAE,eAAe,oBAAoB,EAAE;YAC7C,IAAI,EAAE,iBAAiB,CAAC,WAAW;YACnC,WAAW,EAAE,iBAAiB,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,MAAM,EAAE,cAAc,CAAC,KAAK;YAC5B,aAAa,EAAE,wBAAwB,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE;YACvG,mBAAmB,EAAE,SAAS;SAC/B,CAAC;QACF,OAAO,CACL,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,eAAe,CAAC,EAG7C,EACD,oBAAoB,CACrB,CAAC;QACF,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IACxD,CAAC;AACH,CAAC"}