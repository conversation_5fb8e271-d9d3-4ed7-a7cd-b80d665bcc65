{"version": 3, "file": "errorParsing.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/errorParsing.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,QAAQ,EACR,UAAU,EACV,0BAA0B,EAC1B,oBAAoB,EACpB,uBAAuB,EACvB,2BAA2B,EAC3B,UAAU,EACV,iBAAiB,GAClB,MAAM,yBAAyB,CAAC;AAEjC,8BAA8B;AAC9B,MAAM,kCAAkC,GAAG,CACzC,gBAAwB,0BAA0B,EAClD,EAAE,CACF,2FAA2F,aAAa,sCAAsC,CAAC;AAEjJ,MAAM,0CAA0C,GAAG,CACjD,eAAuB,oBAAoB,EAC3C,gBAAwB,0BAA0B,EAClD,EAAE,CACF,iCAAiC,YAAY,6CAA6C,aAAa,gSAAgS,CAAC;AAE1Y,MAAM,8CAA8C,GAAG,GAAG,EAAE,CAC1D,sSAAsS,CAAC;AAEzS,yCAAyC;AACzC,MAAM,kCAAkC,GAAG,CACzC,gBAAwB,0BAA0B,EAClD,EAAE,CACF,2FAA2F,aAAa,4GAA4G,CAAC;AAEvN,MAAM,0CAA0C,GAAG,CACjD,eAAuB,oBAAoB,EAC3C,gBAAwB,0BAA0B,EAClD,EAAE,CACF,iCAAiC,YAAY,6CAA6C,aAAa,wIAAwI,YAAY,2HAA2H,CAAC;AAEzX,MAAM,8CAA8C,GAAG,CACrD,eAAuB,oBAAoB,EAC3C,EAAE,CACF,8IAA8I,YAAY,2HAA2H,CAAC;AACxR,MAAM,mCAAmC,GACvC,2IAA2I,CAAC;AAC9I,MAAM,+BAA+B,GACnC,wIAAwI,CAAC;AAC3I,MAAM,+BAA+B,GAAG,CACtC,gBAAwB,0BAA0B,EAClD,EAAE,CACF,2FAA2F,aAAa,sCAAsC,CAAC;AAEjJ,SAAS,mBAAmB,CAC1B,QAAmB,EACnB,KAAe,EACf,QAAqB,EACrB,YAAqB,EACrB,aAAsB;IAEtB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAChC,8FAA8F;YAC9F,MAAM,UAAU,GACd,QAAQ,KAAK,UAAU,CAAC,MAAM,IAAI,QAAQ,KAAK,UAAU,CAAC,QAAQ,CAAC;YAErE,IAAI,uBAAuB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnC,OAAO,UAAU;oBACf,CAAC,CAAC,0CAA0C,CACxC,YAAY,IAAI,oBAAoB,EACpC,aAAa,CACd;oBACH,CAAC,CAAC,0CAA0C,CACxC,YAAY,IAAI,oBAAoB,EACpC,aAAa,CACd,CAAC;YACR,CAAC;iBAAM,IAAI,2BAA2B,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9C,OAAO,UAAU;oBACf,CAAC,CAAC,8CAA8C,CAC5C,YAAY,IAAI,oBAAoB,CACrC;oBACH,CAAC,CAAC,8CAA8C,EAAE,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,OAAO,UAAU;oBACf,CAAC,CAAC,kCAAkC,CAAC,aAAa,CAAC;oBACnD,CAAC,CAAC,kCAAkC,CAAC,aAAa,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QACD,KAAK,QAAQ,CAAC,UAAU;YACtB,OAAO,mCAAmC,CAAC;QAC7C,KAAK,QAAQ,CAAC,aAAa;YACzB,OAAO,+BAA+B,CAAC;QACzC;YACE,OAAO,+BAA+B,CAAC,aAAa,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,KAAc,EACd,QAAmB,EACnB,QAAqB,EACrB,YAAqB,EACrB,aAAsB;IAEtB,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,IAAI,IAAI,GAAG,eAAe,KAAK,CAAC,OAAO,GAAG,CAAC;QAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YACzB,IAAI,IAAI,mBAAmB,CACzB,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,aAAa,CACd,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gEAAgE;IAChE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACrB,OAAO,eAAe,KAAK,GAAG,CAAC,CAAC,kCAAkC;QACpE,CAAC;QAED,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAY,CAAC;YACtD,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5B,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC7C,IAAI,CAAC;oBACH,8DAA8D;oBAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAY,CAAC;oBACxD,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC5B,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC;oBAC3C,CAAC;gBACH,CAAC;gBAAC,OAAO,EAAE,EAAE,CAAC;oBACZ,kEAAkE;gBACpE,CAAC;gBACD,IAAI,IAAI,GAAG,eAAe,YAAY,aAAa,WAAW,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC;gBAChF,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBACnC,IAAI,IAAI,mBAAmB,CACzB,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,aAAa,CACd,CAAC;gBACJ,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,kEAAkE;QACpE,CAAC;QACD,OAAO,eAAe,KAAK,GAAG,CAAC;IACjC,CAAC;IAED,OAAO,yCAAyC,CAAC;AACnD,CAAC"}