{"version": 3, "file": "nextSpeakerChecker.js", "sourceRoot": "", "sources": ["../../../src/utils/nextSpeakerChecker.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAwB,IAAI,EAAE,MAAM,eAAe,CAAC;AAC3D,OAAO,EAAE,0BAA0B,EAAE,MAAM,qBAAqB,CAAC;AAGjE,OAAO,EAAE,kBAAkB,EAAE,MAAM,wBAAwB,CAAC;AAE5D,MAAM,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;CAwBpB,CAAC;AAEF,MAAM,eAAe,GAAgB;IACnC,IAAI,EAAE,IAAI,CAAC,MAAM;IACjB,UAAU,EAAE;QACV,SAAS,EAAE;YACT,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,WAAW,EACT,iJAAiJ;SACpJ;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;YACvB,WAAW,EACT,iFAAiF;SACpF;KACF;IACD,QAAQ,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;CACxC,CAAC;AAOF,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,IAAgB,EAChB,YAA0B,EAC1B,WAAwB;IAExB,iHAAiH;IACjH,qHAAqH;IACrH,oHAAoH;IACpH,kEAAkE;IAClE,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAE3D,6CAA6C;IAC7C,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChC,qDAAqD;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IAC/C,uEAAuE;IACvE,iFAAiF;IACjF,sBAAsB;IACtB,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,wBAAwB,GAC5B,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAExD,4EAA4E;IAC5E,oCAAoC;IACpC,IACE,wBAAwB;QACxB,kBAAkB,CAAC,wBAAwB,CAAC,EAC5C,CAAC;QACD,OAAO;YACL,SAAS,EACP,2EAA2E;YAC7E,YAAY,EAAE,OAAO;SACtB,CAAC;IACJ,CAAC;IAED,IACE,wBAAwB;QACxB,wBAAwB,CAAC,IAAI,KAAK,OAAO;QACzC,wBAAwB,CAAC,KAAK;QAC9B,wBAAwB,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAC3C,CAAC;QACD,wBAAwB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO;YACL,SAAS,EACP,oHAAoH;YACtH,YAAY,EAAE,OAAO;SACtB,CAAC;IACJ,CAAC;IAED,0EAA0E;IAE1E,MAAM,WAAW,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QACjD,uEAAuE;QACvE,0BAA0B;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,QAAQ,GAAc;QAC1B,GAAG,cAAc;QACjB,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,EAAE;KAClD,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,CAAC,MAAM,YAAY,CAAC,YAAY,CACrD,QAAQ,EACR,eAAe,EACf,WAAW,EACX,0BAA0B,CAC3B,CAAmC,CAAC;QAErC,IACE,cAAc;YACd,cAAc,CAAC,YAAY;YAC3B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,EACvD,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CACV,gFAAgF,EAChF,KAAK,CACN,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC"}