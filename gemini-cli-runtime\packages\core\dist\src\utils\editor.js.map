{"version": 3, "file": "editor.js", "sourceRoot": "", "sources": ["../../../src/utils/editor.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAWhD,SAAS,iBAAiB,CAAC,MAAc;IACvC,OAAO;QACL,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,KAAK;KACN,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrB,CAAC;AAOD,SAAS,aAAa,CAAC,GAAW;IAChC,IAAI,CAAC;QACH,QAAQ,CACN,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,cAAc,GAAG,EAAE,EACvE,EAAE,KAAK,EAAE,QAAQ,EAAE,CACpB,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,MAAM,cAAc,GAA2D;IAC7E,MAAM,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE;IAC9C,QAAQ,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE;IACpD,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE;IACpD,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE;IAC9C,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE;IACrC,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE;IAC1C,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE;CACtC,CAAC;AAEF,MAAM,UAAU,kBAAkB,CAAC,MAAkB;IACnD,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IAC7C,MAAM,OAAO,GACX,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;IAC7E,OAAO,aAAa,CAAC,OAAO,CAAC,CAAC;AAChC,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,MAAkB;IACzD,MAAM,eAAe,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACzE,OAAO,eAAe,CAAC;IACzB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAA0B;IAC1D,IAAI,MAAM,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,OAAO,kBAAkB,CAAC,MAAM,CAAC,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,OAAe,EACf,MAAkB;IAElB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;IAC7C,MAAM,OAAO,GACX,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;IAC7E,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,QAAQ,CAAC;QACd,KAAK,UAAU,CAAC;QAChB,KAAK,UAAU,CAAC;QAChB,KAAK,QAAQ,CAAC;QACd,KAAK,KAAK;YACR,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;QACnE,KAAK,KAAK,CAAC;QACX,KAAK,QAAQ;YACX,OAAO;gBACL,OAAO;gBACP,IAAI,EAAE;oBACJ,IAAI;oBACJ,yCAAyC;oBACzC,IAAI;oBACJ,MAAM;oBACN,+DAA+D;oBAC/D,IAAI;oBACJ,oCAAoC;oBACpC,0BAA0B;oBAC1B,IAAI;oBACJ,gNAAgN;oBAChN,wBAAwB;oBACxB,IAAI;oBACJ,4GAA4G;oBAC5G,IAAI;oBACJ,2CAA2C;oBAC3C,IAAI;oBACJ,oHAAoH;oBACpH,4CAA4C;oBAC5C,IAAI;oBACJ,yBAAyB;oBACzB,OAAO;oBACP,OAAO;iBACR;aACF,CAAC;QACJ;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,QAAQ,CAC5B,OAAe,EACf,OAAe,EACf,MAAkB;IAElB,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7D,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACrE,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU,CAAC;YAChB,KAAK,UAAU,CAAC;YAChB,KAAK,QAAQ,CAAC;YACd,KAAK,KAAK;gBACR,uEAAuE;gBACvE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,EAAE;wBAChE,KAAK,EAAE,SAAS;wBAChB,KAAK,EAAE,IAAI;qBACZ,CAAC,CAAC;oBAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;wBAChC,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;4BACf,OAAO,EAAE,CAAC;wBACZ,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,MAAM,qBAAqB,IAAI,EAAE,CAAC,CAAC,CAAC;wBAC1D,CAAC;oBACH,CAAC,CAAC,CAAC;oBAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;wBACjC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YAEL,KAAK,KAAK,CAAC;YACX,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,0CAA0C;gBAC1C,MAAM,OAAO,GACX,OAAO,CAAC,QAAQ,KAAK,OAAO;oBAC1B,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBACxD,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtF,QAAQ,CAAC,OAAO,EAAE;oBAChB,KAAK,EAAE,SAAS;oBAChB,QAAQ,EAAE,MAAM;iBACjB,CAAC,CAAC;gBACH,MAAM;YACR,CAAC;YAED;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,MAAM,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;AACH,CAAC"}