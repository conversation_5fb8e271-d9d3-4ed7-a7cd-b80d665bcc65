/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { DEFAULT_GEMINI_FLASH_MODEL } from '../config/models.js';
/**
 * The default summarizer for tool results.
 *
 * @param result The result of the tool execution.
 * @param geminiClient The Gemini client to use for summarization.
 * @param abortSignal The abort signal to use for summarization.
 * @returns The summary of the result.
 */
export const defaultSummarizer = (result, _geminiClient, _abortSignal) => Promise.resolve(JSON.stringify(result.llmContent));
// TODO: Move both these functions to utils
function partToString(part) {
    if (!part) {
        return '';
    }
    if (typeof part === 'string') {
        return part;
    }
    if (Array.isArray(part)) {
        return part.map(partToString).join('');
    }
    if ('text' in part) {
        return part.text ?? '';
    }
    return '';
}
function getResponseText(response) {
    if (response.candidates && response.candidates.length > 0) {
        const candidate = response.candidates[0];
        if (candidate.content &&
            candidate.content.parts &&
            candidate.content.parts.length > 0) {
            return candidate.content.parts
                .filter((part) => part.text)
                .map((part) => part.text)
                .join('');
        }
    }
    return null;
}
const toolOutputSummarizerModel = DEFAULT_GEMINI_FLASH_MODEL;
const toolOutputSummarizerConfig = {
    maxOutputTokens: 2000,
};
const SUMMARIZE_TOOL_OUTPUT_PROMPT = `Summarize the following tool output to be a maximum of {maxLength} characters. The summary should be concise and capture the main points of the tool output.

The summarization should be done based on the content that is provided. Here are the basic rules to follow:
1. If the text is a directory listing or any output that is structural, use the history of the conversation to understand the context. Using this context try to understand what information we need from the tool output and return that as a response.
2. If the text is text content and there is nothing structural that we need, summarize the text.
3. If the text is the output of a shell command, use the history of the conversation to understand the context. Using this context try to understand what information we need from the tool output and return a summarization along with the stack trace of any error within the <error></error> tags. The stack trace should be complete and not truncated. If there are warnings, you should include them in the summary within <warning></warning> tags.


Text to summarize:
"{textToSummarize}"

Return the summary string which should first contain an overall summarization of text followed by the full stack trace of errors and warnings in the tool output.
`;
export const llmSummarizer = (result, geminiClient, abortSignal) => summarizeToolOutput(partToString(result.llmContent), geminiClient, abortSignal);
export async function summarizeToolOutput(textToSummarize, geminiClient, abortSignal, maxLength = 2000) {
    if (!textToSummarize || textToSummarize.length < maxLength) {
        return textToSummarize;
    }
    const prompt = SUMMARIZE_TOOL_OUTPUT_PROMPT.replace('{maxLength}', String(maxLength)).replace('{textToSummarize}', textToSummarize);
    const contents = [{ role: 'user', parts: [{ text: prompt }] }];
    try {
        const parsedResponse = (await geminiClient.generateContent(contents, toolOutputSummarizerConfig, abortSignal, toolOutputSummarizerModel));
        return getResponseText(parsedResponse) || textToSummarize;
    }
    catch (error) {
        console.error('Failed to summarize tool output.', error);
        return textToSummarize;
    }
}
//# sourceMappingURL=summarizer.js.map