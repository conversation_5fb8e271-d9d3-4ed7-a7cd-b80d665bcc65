{"version": 3, "file": "setup.js", "sourceRoot": "", "sources": ["../../../src/code_assist/setup.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAKL,UAAU,GACX,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAG/C,MAAM,OAAO,sBAAuB,SAAQ,KAAK;IAC/C;QACE,KAAK,CACH,wHAAwH,CACzH,CAAC;IACJ,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,SAAS,CAAC,MAAoB;IAClD,IAAI,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,SAAS,CAAC;IAC9D,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAEzD,MAAM,cAAc,GAAmB;QACrC,OAAO,EAAE,iBAAiB;QAC1B,QAAQ,EAAE,sBAAsB;QAChC,UAAU,EAAE,QAAQ;QACpB,WAAW,EAAE,SAAS;KACvB,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC;QAC5C,uBAAuB,EAAE,SAAS;QAClC,QAAQ,EAAE,cAAc;KACzB,CAAC,CAAC;IAEH,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,uBAAuB,EAAE,CAAC;QAClD,SAAS,GAAG,OAAO,CAAC,uBAAuB,CAAC;IAC9C,CAAC;IAED,MAAM,IAAI,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC,kCAAkC,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1D,MAAM,IAAI,sBAAsB,EAAE,CAAC;IACrC,CAAC;IAED,MAAM,UAAU,GAAuB;QACrC,MAAM,EAAE,IAAI,CAAC,EAAE;QACf,uBAAuB,EAAE,SAAS;QAClC,QAAQ,EAAE,cAAc;KACzB,CAAC;IAEF,6DAA6D;IAC7D,IAAI,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IACpD,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACpB,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9C,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC;IACD,OAAO,MAAM,CAAC,QAAQ,EAAE,uBAAuB,EAAE,EAAE,IAAI,EAAE,CAAC;AAC5D,CAAC;AAED,SAAS,cAAc,CAAC,GAA2B;IACjD,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;QACpB,OAAO,GAAG,CAAC,WAAW,CAAC;IACzB,CAAC;IACD,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO;QACL,IAAI,EAAE,EAAE;QACR,WAAW,EAAE,EAAE;QACf,EAAE,EAAE,UAAU,CAAC,MAAM;QACrB,kCAAkC,EAAE,IAAI;KACzC,CAAC;AACJ,CAAC"}