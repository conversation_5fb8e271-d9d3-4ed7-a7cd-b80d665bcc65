{"version": 3, "file": "MarkdownDisplay.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/MarkdownDisplay.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAS3D,+CAA+C;AAE/C,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAC5B,MAAM,yBAAyB,GAAG,CAAC,CAAC;AACpC,MAAM,wBAAwB,GAAG,CAAC,CAAC;AACnC,MAAM,wBAAwB,GAAG,CAAC,CAAC;AAEnC,MAAM,uBAAuB,GAAmC,CAAC,EAC/D,IAAI,EACJ,SAAS,EACT,uBAAuB,EACvB,aAAa,GACd,EAAE,EAAE;IACH,IAAI,CAAC,IAAI;QAAE,OAAO,mBAAK,CAAC;IAExB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,MAAM,WAAW,GAAG,mBAAmB,CAAC;IACxC,MAAM,cAAc,GAAG,6BAA6B,CAAC;IACrD,MAAM,WAAW,GAAG,wBAAwB,CAAC;IAC7C,MAAM,WAAW,GAAG,wBAAwB,CAAC;IAC7C,MAAM,OAAO,GAAG,qBAAqB,CAAC;IACtC,MAAM,aAAa,GAAG,kBAAkB,CAAC;IACzC,MAAM,mBAAmB,GAAG,iDAAiD,CAAC;IAE9E,MAAM,aAAa,GAAsB,EAAE,CAAC;IAC5C,IAAI,WAAW,GAAG,KAAK,CAAC;IACxB,IAAI,aAAa,GAAG,IAAI,CAAC;IACzB,IAAI,gBAAgB,GAAa,EAAE,CAAC;IACpC,IAAI,aAAa,GAAkB,IAAI,CAAC;IACxC,IAAI,cAAc,GAAG,EAAE,CAAC;IACxB,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,SAAS,GAAe,EAAE,CAAC;IAC/B,IAAI,YAAY,GAAa,EAAE,CAAC;IAEhC,SAAS,eAAe,CAAC,KAAsB;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1B,aAAa,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC5B,MAAM,GAAG,GAAG,QAAQ,KAAK,EAAE,CAAC;QAE5B,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YAC9C,IACE,UAAU;gBACV,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC3C,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,EAC7C,CAAC;gBACD,eAAe,CACb,KAAC,eAAe,IAEd,OAAO,EAAE,gBAAgB,EACzB,IAAI,EAAE,aAAa,EACnB,SAAS,EAAE,SAAS,EACpB,uBAAuB,EAAE,uBAAuB,EAChD,aAAa,EAAE,aAAa,IALvB,GAAG,CAMR,CACH,CAAC;gBACF,WAAW,GAAG,KAAK,CAAC;gBACpB,gBAAgB,GAAG,EAAE,CAAC;gBACtB,aAAa,GAAG,IAAI,CAAC;gBACrB,cAAc,GAAG,EAAE,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAChD,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAE5D,IAAI,cAAc,EAAE,CAAC;YACnB,WAAW,GAAG,IAAI,CAAC;YACnB,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACnC,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QAC5C,CAAC;aAAM,IAAI,aAAa,IAAI,CAAC,OAAO,EAAE,CAAC;YACrC,0DAA0D;YAC1D,IACE,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;gBACxB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAC3C,CAAC;gBACD,OAAO,GAAG,IAAI,CAAC;gBACf,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBACtE,SAAS,GAAG,EAAE,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,qCAAqC;gBACrC,eAAe,CACb,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,YACf,KAAC,YAAY,IAAC,IAAI,EAAE,IAAI,GAAI,GACvB,IAHC,GAAG,CAIP,CACP,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,IAAI,mBAAmB,EAAE,CAAC;YAC1C,wCAAwC;QAC1C,CAAC;aAAM,IAAI,OAAO,IAAI,aAAa,EAAE,CAAC;YACpC,gBAAgB;YAChB,MAAM,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACrE,8CAA8C;YAC9C,OAAO,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC;gBAC1C,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjB,CAAC;YACD,IAAI,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC;gBACvC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;YACrC,CAAC;YACD,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,eAAe;YACf,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,eAAe,CACb,KAAC,WAAW,IAEV,OAAO,EAAE,YAAY,EACrB,IAAI,EAAE,SAAS,EACf,aAAa,EAAE,aAAa,IAHvB,SAAS,aAAa,CAAC,MAAM,EAAE,CAIpC,CACH,CAAC;YACJ,CAAC;YACD,OAAO,GAAG,KAAK,CAAC;YAChB,SAAS,GAAG,EAAE,CAAC;YACf,YAAY,GAAG,EAAE,CAAC;YAElB,iCAAiC;YACjC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,eAAe,CACb,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,YACf,KAAC,YAAY,IAAC,IAAI,EAAE,IAAI,GAAI,GACvB,IAHC,GAAG,CAIP,CACP,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,EAAE,CAAC;YACnB,eAAe,CACb,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,QAAQ,0BAAW,IADjB,GAAG,CAEP,CACP,CAAC;QACJ,CAAC;aAAM,IAAI,WAAW,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACpC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,UAAU,GAAoB,IAAI,CAAC;YACvC,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,CAAC;oBACJ,UAAU,GAAG,CACX,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YACjC,KAAC,YAAY,IAAC,IAAI,EAAE,UAAU,GAAI,GAC7B,CACR,CAAC;oBACF,MAAM;gBACR,KAAK,CAAC;oBACJ,UAAU,GAAG,CACX,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YACjC,KAAC,YAAY,IAAC,IAAI,EAAE,UAAU,GAAI,GAC7B,CACR,CAAC;oBACF,MAAM;gBACR,KAAK,CAAC;oBACJ,UAAU,GAAG,CACX,KAAC,IAAI,IAAC,IAAI,kBACR,KAAC,YAAY,IAAC,IAAI,EAAE,UAAU,GAAI,GAC7B,CACR,CAAC;oBACF,MAAM;gBACR,KAAK,CAAC;oBACJ,UAAU,GAAG,CACX,KAAC,IAAI,IAAC,MAAM,QAAC,KAAK,EAAE,MAAM,CAAC,IAAI,YAC7B,KAAC,YAAY,IAAC,IAAI,EAAE,UAAU,GAAI,GAC7B,CACR,CAAC;oBACF,MAAM;gBACR;oBACE,UAAU,GAAG,CACX,KAAC,IAAI,cACH,KAAC,YAAY,IAAC,IAAI,EAAE,UAAU,GAAI,GAC7B,CACR,CAAC;oBACF,MAAM;YACV,CAAC;YACD,IAAI,UAAU;gBAAE,eAAe,CAAC,KAAC,GAAG,cAAY,UAAU,IAAhB,GAAG,CAAoB,CAAC,CAAC;QACrE,CAAC;aAAM,IAAI,OAAO,EAAE,CAAC;YACnB,MAAM,iBAAiB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC5B,eAAe,CACb,KAAC,cAAc,IAEb,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAC,IAAI,EACT,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,iBAAiB,IAJ/B,GAAG,CAKR,CACH,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,EAAE,CAAC;YACnB,MAAM,iBAAiB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC5B,eAAe,CACb,KAAC,cAAc,IAEb,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAC,IAAI,EACT,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,iBAAiB,IAJ/B,GAAG,CAKR,CACH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC7C,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,aAAa,CAAC,IAAI,CAChB,KAAC,GAAG,IAAyB,MAAM,EAAE,iBAAiB,IAA5C,UAAU,KAAK,EAAE,CAA+B,CAC3D,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC;gBACvB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,eAAe,CACb,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,YACf,KAAC,YAAY,IAAC,IAAI,EAAE,IAAI,GAAI,GACvB,IAHC,GAAG,CAIP,CACP,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,WAAW,EAAE,CAAC;QAChB,eAAe,CACb,KAAC,eAAe,IAEd,OAAO,EAAE,gBAAgB,EACzB,IAAI,EAAE,aAAa,EACnB,SAAS,EAAE,SAAS,EACpB,uBAAuB,EAAE,uBAAuB,EAChD,aAAa,EAAE,aAAa,IALxB,UAAU,CAMd,CACH,CAAC;IACJ,CAAC;IAED,iCAAiC;IACjC,IAAI,OAAO,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/D,eAAe,CACb,KAAC,WAAW,IAEV,OAAO,EAAE,YAAY,EACrB,IAAI,EAAE,SAAS,EACf,aAAa,EAAE,aAAa,IAHvB,SAAS,aAAa,CAAC,MAAM,EAAE,CAIpC,CACH,CAAC;IACJ,CAAC;IAED,OAAO,4BAAG,aAAa,GAAI,CAAC;AAC9B,CAAC,CAAC;AAYF,MAAM,uBAAuB,GAAmC,CAAC,EAC/D,OAAO,EACP,IAAI,EACJ,SAAS,EACT,uBAAuB,EACvB,aAAa,GACd,EAAE,EAAE;IACH,MAAM,qBAAqB,GAAG,CAAC,CAAC,CAAC,6DAA6D;IAC9F,MAAM,cAAc,GAAG,CAAC,CAAC,CAAC,8DAA8D;IAExF,IAAI,SAAS,IAAI,uBAAuB,KAAK,SAAS,EAAE,CAAC;QACvD,MAAM,2BAA2B,GAAG,IAAI,CAAC,GAAG,CAC1C,CAAC,EACD,uBAAuB,GAAG,cAAc,CACzC,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,GAAG,2BAA2B,EAAE,CAAC;YACjD,IAAI,2BAA2B,GAAG,qBAAqB,EAAE,CAAC;gBACxD,yDAAyD;gBACzD,OAAO,CACL,KAAC,GAAG,IAAC,WAAW,EAAE,yBAAyB,YACzC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,8CAAsC,GAC1D,CACP,CAAC;YACJ,CAAC;YACD,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,2BAA2B,CAAC,CAAC;YACvE,MAAM,sBAAsB,GAAG,YAAY,CACzC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC3B,IAAI,EACJ,uBAAuB,EACvB,aAAa,GAAG,yBAAyB,CAC1C,CAAC;YACF,OAAO,CACL,MAAC,GAAG,IAAC,WAAW,EAAE,yBAAyB,EAAE,aAAa,EAAC,QAAQ,aAChE,sBAAsB,EACvB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,wCAAgC,IACpD,CACP,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,MAAM,aAAa,GAAG,YAAY,CAChC,WAAW,EACX,IAAI,EACJ,uBAAuB,EACvB,aAAa,GAAG,yBAAyB,CAC1C,CAAC;IAEF,OAAO,CACL,KAAC,GAAG,IACF,WAAW,EAAE,yBAAyB,EACtC,aAAa,EAAC,QAAQ,EACtB,KAAK,EAAE,aAAa,EACpB,UAAU,EAAE,CAAC,YAEZ,aAAa,GACV,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAS5D,MAAM,sBAAsB,GAAkC,CAAC,EAC7D,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,iBAAiB,GAAG,EAAE,GACvB,EAAE,EAAE;IACH,MAAM,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC;IAC5D,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;IAClC,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC;IAE7C,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAE,WAAW,GAAG,wBAAwB,EACnD,aAAa,EAAC,KAAK,aAEnB,KAAC,GAAG,IAAC,KAAK,EAAE,WAAW,YACrB,KAAC,IAAI,cAAE,MAAM,GAAQ,GACjB,EACN,KAAC,GAAG,IAAC,QAAQ,EAAE,wBAAwB,YACrC,KAAC,IAAI,IAAC,IAAI,EAAC,MAAM,YACf,KAAC,YAAY,IAAC,IAAI,EAAE,QAAQ,GAAI,GAC3B,GACH,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AAQ1D,MAAM,mBAAmB,GAA+B,CAAC,EACvD,OAAO,EACP,IAAI,EACJ,aAAa,GACd,EAAE,EAAE,CAAC,CACJ,KAAC,aAAa,IAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa,GAAI,CAC9E,CAAC;AAEF,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAEpD,MAAM,CAAC,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC"}