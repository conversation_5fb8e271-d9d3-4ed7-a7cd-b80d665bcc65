{"version": 3, "file": "useAuthCommand.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useAuthCommand.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAEzD,OAAO,EACL,QAAQ,EAER,yBAAyB,EACzB,eAAe,GAChB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAExD,MAAM,CAAC,MAAM,cAAc,GAAG,CAC5B,QAAwB,EACxB,YAA4C,EAC5C,MAAc,EACd,EAAE;IACF,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CACtD,QAAQ,CAAC,MAAM,CAAC,gBAAgB,KAAK,SAAS,CAC/C,CAAC;IAEF,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;QACtC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAEhE,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE;YAC1B,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAClD,IAAI,gBAAgB,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBACH,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,IAAI,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,YAAY,CAAC,6BAA6B,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChE,cAAc,EAAE,CAAC;YACnB,CAAC;oBAAS,CAAC;gBACT,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC;QAEF,KAAK,QAAQ,EAAE,CAAC;IAClB,CAAC,EAAE,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;IAEvE,MAAM,gBAAgB,GAAG,WAAW,CAClC,KAAK,EAAE,QAA8B,EAAE,KAAmB,EAAE,EAAE;QAC5D,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,yBAAyB,EAAE,CAAC;YAClC,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACvD,IAAI,QAAQ,KAAK,QAAQ,CAAC,iBAAiB,IAAI,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC;gBACrE,cAAc,EAAE,CAAC;gBACjB,OAAO,CAAC,GAAG,CACT;;;;aAIC,CACF,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QACD,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAC3B,YAAY,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC,EACD,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,CACjC,CAAC;IAEF,MAAM,oBAAoB,GAAG,WAAW,CAAC,GAAG,EAAE;QAC5C,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,gBAAgB;QAChB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;KACrB,CAAC;AACJ,CAAC,CAAC"}