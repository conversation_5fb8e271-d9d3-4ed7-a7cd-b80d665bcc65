{"version": 3, "file": "uiTelemetry.js", "sourceRoot": "", "sources": ["../../../src/telemetry/uiTelemetry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EACL,eAAe,EACf,kBAAkB,EAClB,eAAe,GAChB,MAAM,gBAAgB,CAAC;AAExB,OAAO,EAIL,gBAAgB,GACjB,MAAM,YAAY,CAAC;AAmDpB,MAAM,yBAAyB,GAAG,GAAiB,EAAE,CAAC,CAAC;IACrD,GAAG,EAAE;QACH,aAAa,EAAE,CAAC;QAChB,WAAW,EAAE,CAAC;QACd,cAAc,EAAE,CAAC;KAClB;IACD,MAAM,EAAE;QACN,MAAM,EAAE,CAAC;QACT,UAAU,EAAE,CAAC;QACb,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;QACT,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,CAAC;KACR;CACF,CAAC,CAAC;AAEH,MAAM,oBAAoB,GAAG,GAAmB,EAAE,CAAC,CAAC;IAClD,MAAM,EAAE,EAAE;IACV,KAAK,EAAE;QACL,UAAU,EAAE,CAAC;QACb,YAAY,EAAE,CAAC;QACf,SAAS,EAAE,CAAC;QACZ,eAAe,EAAE,CAAC;QAClB,cAAc,EAAE;YACd,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5B,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5B,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;SAC7B;QACD,MAAM,EAAE,EAAE;KACX;CACF,CAAC,CAAC;AAEH,MAAM,OAAO,kBAAmB,SAAQ,YAAY;IAClD,QAAQ,GAAmB,oBAAoB,EAAE,CAAC;IAClD,qBAAqB,GAAG,CAAC,CAAC;IAE1B,QAAQ,CAAC,KAAc;QACrB,QAAQ,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5B,KAAK,kBAAkB;gBACrB,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBAC/B,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;YACR;gBACE,wDAAwD;gBACxD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,oBAAoB,EAAE,IAAI,CAAC,qBAAqB;SACjD,CAAC,CAAC;IACL,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,uBAAuB;QACrB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAEO,uBAAuB,CAAC,SAAiB;QAC/C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,yBAAyB,EAAE,CAAC;QAChE,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAEO,kBAAkB,CAAC,KAAuB;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAE/D,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QACjC,YAAY,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC,WAAW,CAAC;QAErD,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC;QACtD,YAAY,CAAC,MAAM,CAAC,UAAU,IAAI,KAAK,CAAC,kBAAkB,CAAC;QAC3D,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,iBAAiB,CAAC;QACrD,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;QAC/D,YAAY,CAAC,MAAM,CAAC,QAAQ,IAAI,KAAK,CAAC,oBAAoB,CAAC;QAC3D,YAAY,CAAC,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,gBAAgB,CAAC;QAEnD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,iBAAiB,CAAC;IACvD,CAAC;IAEO,eAAe,CAAC,KAAoB;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/D,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QACjC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QAC/B,YAAY,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC,WAAW,CAAC;IACvD,CAAC;IAEO,eAAe,CAAC,KAAoB;QAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,WAAW,CAAC;QAE3C,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK,CAAC,YAAY,EAAE,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;YACvC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG;gBAClC,KAAK,EAAE,CAAC;gBACR,OAAO,EAAE,CAAC;gBACV,IAAI,EAAE,CAAC;gBACP,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE;oBACT,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC5B,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC5B,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;iBAC7B;aACF,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACpD,SAAS,CAAC,KAAK,EAAE,CAAC;QAClB,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC;QAC1C,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,SAAS,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;CACF;AAED,MAAM,CAAC,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC"}