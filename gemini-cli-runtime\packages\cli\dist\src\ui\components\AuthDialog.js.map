{"version": 3, "file": "AuthDialog.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/AuthDialog.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACxC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAkB,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxE,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAQ1D,SAAS,oBAAoB,CAC3B,eAAmC;IAEnC,IACE,eAAe;QACf,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,eAA2B,CAAC,EAC7D,CAAC;QACD,OAAO,eAA2B,CAAC;IACrC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,EACzB,QAAQ,EACR,QAAQ,EACR,mBAAmB,GACH;IAChB,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAgB,GAAG,EAAE;QACnE,IAAI,mBAAmB,EAAE,CAAC;YACxB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,MAAM,eAAe,GAAG,oBAAoB,CAC1C,OAAO,CAAC,GAAG,CAAC,wBAAwB,CACrC,CAAC;QAEF,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;YACrE,OAAO,CACL,gDAAgD,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK;gBACzF,qBAAqB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAC3D,CAAC;QACJ,CAAC;QAED,IACE,OAAO,CAAC,GAAG,CAAC,cAAc;YAC1B,CAAC,CAAC,eAAe,IAAI,eAAe,KAAK,QAAQ,CAAC,UAAU,CAAC,EAC7D,CAAC;YACD,OAAO,uFAAuF,CAAC;QACjG,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IACH,MAAM,KAAK,GAAG;QACZ;YACE,KAAK,EAAE,mBAAmB;YAC1B,KAAK,EAAE,QAAQ,CAAC,iBAAiB;SAClC;QACD,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM;YACpC,CAAC,CAAC;gBACE;oBACE,KAAK,EAAE,kCAAkC;oBACzC,KAAK,EAAE,QAAQ,CAAC,WAAW;iBAC5B;aACF;YACH,CAAC,CAAC,EAAE,CAAC;QACP;YACE,KAAK,EAAE,oBAAoB;YAC3B,KAAK,EAAE,QAAQ,CAAC,UAAU;SAC3B;QACD,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,aAAa,EAAE;KACtD,CAAC;IAEF,MAAM,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE;QAChD,IAAI,QAAQ,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACzD,CAAC;QAED,MAAM,eAAe,GAAG,oBAAoB,CAC1C,OAAO,CAAC,GAAG,CAAC,wBAAwB,CACrC,CAAC;QACF,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC;QACxC,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,UAAU,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,iBAAiB,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,CAAC,UAAoB,EAAE,EAAE;QAChD,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,eAAe,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,CAAC;YACtB,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC,CAAC;IAEF,QAAQ,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;QACvB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,6CAA6C;YAC7C,iDAAiD;YACjD,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO;YACT,CAAC;YACD,IAAI,QAAQ,CAAC,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBACnD,2CAA2C;gBAC3C,eAAe,CACb,wEAAwE,CACzE,CAAC;gBACF,OAAO;YACT,CAAC;YACD,QAAQ,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CACL,MAAC,GAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,IAAI,EACxB,aAAa,EAAC,QAAQ,EACtB,OAAO,EAAE,CAAC,EACV,KAAK,EAAC,MAAM,aAEZ,KAAC,IAAI,IAAC,IAAI,kCAAmB,EAC7B,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,uEAA4D,GAC7D,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,iBAAiB,IAChB,KAAK,EAAE,KAAK,EACZ,YAAY,EAAE,gBAAgB,EAC9B,QAAQ,EAAE,gBAAgB,EAC1B,SAAS,EAAE,IAAI,GACf,GACE,EACL,YAAY,IAAI,CACf,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,YAAG,YAAY,GAAQ,GAChD,CACP,EACD,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,sCAA8B,GAClD,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,sEAA2D,GAC5D,EACN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YAE1B,2EAA2E,GAExE,GACH,IACF,CACP,CAAC;AACJ,CAAC"}