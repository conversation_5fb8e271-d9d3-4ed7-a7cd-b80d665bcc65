Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9B00) msys-2.0.dll+0x1FEBA
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210285FF9, 0007FFFFAAB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAC00  0002100690B4 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAEE0  00021006A49D (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD4AD80000 ntdll.dll
7FFD491E0000 KERNEL32.DLL
7FFD48010000 KERNELBASE.dll
7FFD49CF0000 USER32.dll
7FFD48AF0000 win32u.dll
000210040000 msys-2.0.dll
7FFD494A0000 GDI32.dll
7FFD47ED0000 gdi32full.dll
7FFD48580000 msvcp_win.dll
7FFD487B0000 ucrtbase.dll
7FFD49110000 advapi32.dll
7FFD49EF0000 msvcrt.dll
7FFD492B0000 sechost.dll
7FFD4A150000 RPCRT4.dll
7FFD475F0000 CRYPTBASE.DLL
7FFD48A50000 bcryptPrimitives.dll
7FFD49CB0000 IMM32.DLL
