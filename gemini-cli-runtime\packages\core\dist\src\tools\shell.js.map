{"version": 3, "file": "shell.js", "sourceRoot": "", "sources": ["../../../src/tools/shell.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,OAAO,EACL,QAAQ,EAIR,uBAAuB,GACxB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,SAAS,MAAM,YAAY,CAAC;AAOnC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAE7D,MAAM,yBAAyB,GAAG,IAAI,CAAC;AAEvC,MAAM,OAAO,SAAU,SAAQ,QAAqC;IAIrC;IAH7B,MAAM,CAAC,IAAI,GAAW,mBAAmB,CAAC;IAClC,SAAS,GAAgB,IAAI,GAAG,EAAE,CAAC;IAE3C,YAA6B,MAAc;QACzC,KAAK,CACH,SAAS,CAAC,IAAI,EACd,OAAO,EACP;;;;;;;;;;;;wDAYkD,EAClD;YACE,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,IAAI,CAAC,MAAM;oBACjB,WAAW,EAAE,sDAAsD;iBACpE;gBACD,WAAW,EAAE;oBACX,IAAI,EAAE,IAAI,CAAC,MAAM;oBACjB,WAAW,EACT,0JAA0J;iBAC7J;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,IAAI,CAAC,MAAM;oBACjB,WAAW,EACT,uJAAuJ;iBAC1J;aACF;YACD,QAAQ,EAAE,CAAC,SAAS,CAAC;SACtB,EACD,KAAK,EAAE,yBAAyB;QAChC,IAAI,CACL,CAAC;QAvCyB,WAAM,GAAN,MAAM,CAAQ;IAwC3C,CAAC;IAED,cAAc,CAAC,MAAuB;QACpC,IAAI,WAAW,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QACtC,iCAAiC;QACjC,2EAA2E;QAC3E,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,WAAW,IAAI,QAAQ,MAAM,CAAC,SAAS,GAAG,CAAC;QAC7C,CAAC;QACD,uEAAuE;QACvE,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,WAAW,IAAI,KAAK,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC;QAChE,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;;;;;OAQG;IACH,cAAc,CAAC,OAAe;QAC5B,OAAO,OAAO;aACX,IAAI,EAAE,CAAC,yCAAyC;aAChD,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,gCAAgC;aACvD,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,iFAAiF;YACvG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,oFAAoF;aACpG,GAAG,EAAE,CAAC,CAAC,mFAAmF;IAC/F,CAAC;IAED;;;;;;OAMG;IACH,gBAAgB,CAAC,OAAe;QAC9B,mCAAmC;QACnC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EACJ,oEAAoE;aACvE,CAAC;QACJ,CAAC;QAED,MAAM,gBAAgB,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;QAE1D,MAAM,SAAS,GAAG,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAE3E;;;;;;WAMG;QACH,MAAM,YAAY,GAAG,CAAC,GAAW,EAAE,MAAc,EAAW,EAAE;YAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;QACpE,CAAC,CAAC;QAEF;;;WAGG;QACH,MAAM,eAAe,GAAG,CAAC,KAAe,EAAY,EAAE,CACpD,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrB,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;gBACxC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC1D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEL,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC;QAEzD,mDAAmD;QACnD,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YACjE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,kDAAkD;aAC3D,CAAC;QACJ,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;QAC/D,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;QAE5D,MAAM,0BAA0B,GAAG,eAAe,CAAC,IAAI,GAAG,CAAC,CAAC;QAC5D,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CACvD,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CACzB,CAAC;QAEF,MAAM,kBAAkB,GAAG,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAExE,MAAM,kBAAkB,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC;QAEhD,KAAK,MAAM,GAAG,IAAI,kBAAkB,EAAE,CAAC;YACrC,+CAA+C;YAC/C,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CACpD,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAC3B,CAAC;YACF,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,YAAY,GAAG,+BAA+B;iBACvD,CAAC;YACJ,CAAC;YAED,sEAAsE;YACtE,MAAM,iBAAiB,GACrB,0BAA0B,IAAI,CAAC,iBAAiB,CAAC;YACnD,MAAM,kBAAkB,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC;YAChD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CACpD,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAC3B,CAAC;gBACF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,YAAY,GAAG,uCAAuC;qBAC/D,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,iDAAiD;QACjD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED,kBAAkB,CAAC,MAAuB;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;gBACzB,OAAO,CAAC,KAAK,CACX,8DAA8D,CAC/D,CAAC;gBACF,OAAO,2BAA2B,MAAM,CAAC,OAAO,EAAE,CAAC;YACrD,CAAC;YACD,OAAO,YAAY,CAAC,MAAM,CAAC;QAC7B,CAAC;QACD,MAAM,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACxE,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YAC3B,OAAO,0BAA0B,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACzC,OAAO,iEAAiE,CAAC;QAC3E,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,OAAO,+EAA+E,CAAC;YACzF,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAC5B,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAC1B,MAAM,CAAC,SAAS,CACjB,CAAC;YACF,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO,uBAAuB,CAAC;YACjC,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,MAAuB,EACvB,YAAyB;QAEzB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC,CAAC,wDAAwD;QACxE,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAE,CAAC,CAAC,2CAA2C;QACrG,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC,CAAC,mCAAmC;QACnD,CAAC;QACD,MAAM,mBAAmB,GAAmC;YAC1D,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,uBAAuB;YAC9B,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,WAAW;YACX,SAAS,EAAE,KAAK,EAAE,OAAgC,EAAE,EAAE;gBACpD,IAAI,OAAO,KAAK,uBAAuB,CAAC,aAAa,EAAE,CAAC;oBACtD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;SACF,CAAC;QACF,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAAuB,EACvB,WAAwB,EACxB,YAAsC;QAEtC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE;oBACV,qBAAqB,MAAM,CAAC,OAAO,EAAE;oBACrC,WAAW,eAAe,EAAE;iBAC7B,CAAC,IAAI,CAAC,IAAI,CAAC;gBACZ,aAAa,EAAE,UAAU,eAAe,EAAE;aAC3C,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO;gBACL,UAAU,EAAE,sDAAsD;gBAClE,aAAa,EAAE,4BAA4B;aAC5C,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC;QAC5C,MAAM,YAAY,GAAG,eAAe,MAAM;aACvC,WAAW,CAAC,CAAC,CAAC;aACd,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,CAAC;QAE1D,qEAAqE;QACrE,MAAM,OAAO,GAAG,SAAS;YACvB,CAAC,CAAC,MAAM,CAAC,OAAO;YAChB,CAAC,CAAC,CAAC,GAAG,EAAE;gBACJ,uEAAuE;gBACvE,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAAE,OAAO,IAAI,GAAG,CAAC;gBAC3C,OAAO,KAAK,OAAO,8BAA8B,YAAY,sBAAsB,CAAC;YACtF,CAAC,CAAC,EAAE,CAAC;QAET,0EAA0E;QAC1E,MAAM,KAAK,GAAG,SAAS;YACrB,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBAChC,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;gBACjC,oFAAoF;gBACpF,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;aACtE,CAAC;YACJ,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBAC7B,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;gBACjC,QAAQ,EAAE,IAAI,EAAE,iEAAiE;gBACjF,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;aACtE,CAAC,CAAC;QAEP,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEhC,MAAM,YAAY,GAAG,CAAC,GAAW,EAAE,EAAE;YACnC,MAAM,IAAI,GAAG,CAAC;YACd,IACE,YAAY;gBACZ,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,GAAG,yBAAyB,EACvD,CAAC;gBACD,YAAY,CAAC,MAAM,CAAC,CAAC;gBACrB,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC;QAEF,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YACvC,yDAAyD;YACzD,mEAAmE;YACnE,kFAAkF;YAClF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACvC,MAAM,IAAI,GAAG,CAAC;gBACd,YAAY,CAAC,GAAG,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YACvC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACvC,MAAM,IAAI,GAAG,CAAC;gBACd,YAAY,CAAC,GAAG,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,GAAiB,IAAI,CAAC;QAC/B,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;YAC/B,KAAK,GAAG,GAAG,CAAC;YACZ,sDAAsD;YACtD,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,GAAkB,IAAI,CAAC;QAC/B,IAAI,aAAa,GAA0B,IAAI,CAAC;QAChD,MAAM,WAAW,GAAG,CAClB,KAAoB,EACpB,OAA8B,EAC9B,EAAE;YACF,MAAM,GAAG,IAAI,CAAC;YACd,IAAI,GAAG,KAAK,CAAC;YACb,aAAa,GAAG,OAAO,CAAC;QAC1B,CAAC,CAAC;QACF,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAE9B,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACzB,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;oBAC9B,qDAAqD;oBACrD,KAAK,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH,kDAAkD;wBAClD,8CAA8C;wBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;wBACpC,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;wBACzD,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;4BACzB,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBAAC,OAAO,EAAE,EAAE,CAAC;wBACZ,kEAAkE;wBAClE,IAAI,CAAC;4BACH,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC;gCACd,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;4BACxB,CAAC;wBACH,CAAC;wBAAC,OAAO,EAAE,EAAE,CAAC;4BACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,CAAC;wBACpE,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QACF,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAEpD,6BAA6B;QAC7B,IAAI,CAAC;YACH,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;QAC5D,CAAC;gBAAS,CAAC;YACT,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC;QAED,8DAA8D;QAC9D,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;YAC9B,IAAI,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,MAAM,UAAU,GAAG,EAAE;qBAClB,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;qBAClC,KAAK,CAAC,IAAI,CAAC;qBACX,MAAM,CAAC,OAAO,CAAC,CAAC;gBACnB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;oBAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACxB,OAAO,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;oBAClC,CAAC;oBACD,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;oBACzB,mCAAmC;oBACnC,IAAI,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,CAAC;wBACtB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC3B,CAAC;gBACH,CAAC;gBACD,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBACzB,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,UAAU,GAAG,yDAAyD,CAAC;YACvE,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;gBAClB,UAAU,IAAI,yEAAyE,MAAM,EAAE,CAAC;YAClG,CAAC;iBAAM,CAAC;gBACN,UAAU,IAAI,+CAA+C,CAAC;YAChE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,UAAU,GAAG;gBACX,YAAY,MAAM,CAAC,OAAO,EAAE;gBAC5B,cAAc,MAAM,CAAC,SAAS,IAAI,QAAQ,EAAE;gBAC5C,WAAW,MAAM,IAAI,SAAS,EAAE;gBAChC,WAAW,MAAM,IAAI,SAAS,EAAE;gBAChC,UAAU,KAAK,IAAI,QAAQ,EAAE;gBAC7B,cAAc,IAAI,IAAI,QAAQ,EAAE;gBAChC,WAAW,aAAa,IAAI,QAAQ,EAAE;gBACtC,oBAAoB,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;gBAClF,uBAAuB,KAAK,CAAC,GAAG,IAAI,QAAQ,EAAE;aAC/C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACf,CAAC;QAED,IAAI,oBAAoB,GAAG,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC;YAC/B,oBAAoB,GAAG,UAAU,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;gBAClB,oBAAoB,GAAG,MAAM,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,iFAAiF;gBACjF,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;oBACxB,oBAAoB,GAAG,4BAA4B,CAAC;gBACtD,CAAC;qBAAM,IAAI,aAAa,EAAE,CAAC;oBACzB,oBAAoB,GAAG,iCAAiC,aAAa,EAAE,CAAC;gBAC1E,CAAC;qBAAM,IAAI,KAAK,EAAE,CAAC;oBACjB,qEAAqE;oBACrE,oBAAoB,GAAG,mBAAmB,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrE,CAAC;qBAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACvC,oBAAoB,GAAG,6BAA6B,IAAI,EAAE,CAAC;gBAC7D,CAAC;gBACD,4EAA4E;gBAC5E,yDAAyD;YAC3D,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,mBAAmB,CACvC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,EAC7B,WAAW,CACZ,CAAC;QAEF,OAAO;YACL,UAAU,EAAE,OAAO;YACnB,aAAa,EAAE,oBAAoB;SACpC,CAAC;IACJ,CAAC"}