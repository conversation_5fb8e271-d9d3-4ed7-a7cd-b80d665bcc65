{"version": 3, "file": "modelCheck.js", "sourceRoot": "", "sources": ["../../../src/core/modelCheck.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,oBAAoB,EACpB,0BAA0B,GAC3B,MAAM,qBAAqB,CAAC;AAE7B;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,MAAc,EACd,sBAA8B;IAE9B,IAAI,sBAAsB,KAAK,oBAAoB,EAAE,CAAC;QACpD,2FAA2F;QAC3F,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED,MAAM,WAAW,GAAG,oBAAoB,CAAC;IACzC,MAAM,aAAa,GAAG,0BAA0B,CAAC;IACjD,MAAM,QAAQ,GAAG,2DAA2D,WAAW,wBAAwB,MAAM,EAAE,CAAC;IACxH,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1B,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QACzC,gBAAgB,EAAE;YAChB,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,IAAI,EAAE,CAAC;YACP,cAAc,EAAE,EAAE,cAAc,EAAE,GAAG,EAAE,eAAe,EAAE,KAAK,EAAE;SAChE;KACF,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,gCAAgC;IAE9F,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE;YACrC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,IAAI;YACJ,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC,CAAC;QAEH,YAAY,CAAC,SAAS,CAAC,CAAC;QAExB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CACT,iCAAiC,WAAW,8CAA8C,aAAa,oBAAoB,CAC5H,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,mFAAmF;QACnF,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAAC,OAAO,MAAM,EAAE,CAAC;QAChB,YAAY,CAAC,SAAS,CAAC,CAAC;QACxB,oEAAoE;QACpE,OAAO,sBAAsB,CAAC;IAChC,CAAC;AACH,CAAC"}