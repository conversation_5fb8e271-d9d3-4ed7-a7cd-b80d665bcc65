{"version": 3, "file": "tool-registry.js", "sourceRoot": "", "sources": ["../../../src/tools/tool-registry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAA+B,IAAI,EAAE,MAAM,eAAe,CAAC;AAClE,OAAO,EAAoB,QAAQ,EAAE,MAAM,YAAY,CAAC;AAExD,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAIpC,MAAM,OAAO,cAAe,SAAQ,QAAgC;IAE/C;IACR;IACA;IACA;IAJX,YACmB,MAAc,EACtB,IAAY,EACZ,WAAmB,EACnB,eAAwC;QAEjD,MAAM,YAAY,GAAG,MAAM,CAAC,uBAAuB,EAAG,CAAC;QACvD,MAAM,WAAW,GAAG,MAAM,CAAC,kBAAkB,EAAG,CAAC;QACjD,WAAW,IAAI;;uEAEoD,YAAY;oDAC/B,WAAW,IAAI,IAAI;;;;;;;;;;;;CAYtE,CAAC;QACE,KAAK,CACH,IAAI,EACJ,IAAI,EACJ,WAAW,EACX,eAAe,EACf,KAAK,EAAE,mBAAmB;QAC1B,KAAK,CACN,CAAC;QA9Be,WAAM,GAAN,MAAM,CAAQ;QACtB,SAAI,GAAJ,IAAI,CAAQ;QACZ,gBAAW,GAAX,WAAW,CAAQ;QACnB,oBAAe,GAAf,eAAe,CAAyB;IA4BnD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAkB;QAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAG,CAAC;QACtD,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;QAElB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,KAAK,GAAiB,IAAI,CAAC;QAC/B,IAAI,IAAI,GAAkB,IAAI,CAAC;QAC/B,IAAI,MAAM,GAA0B,IAAI,CAAC;QAEzC,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YAClC,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,EAAE;gBAChC,MAAM,IAAI,IAAI,EAAE,QAAQ,EAAE,CAAC;YAC7B,CAAC,CAAC;YAEF,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,EAAE;gBAChC,MAAM,IAAI,IAAI,EAAE,QAAQ,EAAE,CAAC;YAC7B,CAAC,CAAC;YAEF,MAAM,OAAO,GAAG,CAAC,GAAU,EAAE,EAAE;gBAC7B,KAAK,GAAG,GAAG,CAAC;YACd,CAAC,CAAC;YAEF,MAAM,OAAO,GAAG,CACd,KAAoB,EACpB,OAA8B,EAC9B,EAAE;gBACF,IAAI,GAAG,KAAK,CAAC;gBACb,MAAM,GAAG,OAAO,CAAC;gBACjB,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;YAEF,MAAM,OAAO,GAAG,GAAG,EAAE;gBACnB,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC9C,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC9C,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACvC,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACvC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;oBACpB,KAAK,CAAC,UAAU,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC,CAAC;YAEF,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAClC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAClC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC3B,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,uGAAuG;QACvG,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;YAC5C,MAAM,UAAU,GAAG;gBACjB,WAAW,MAAM,IAAI,SAAS,EAAE;gBAChC,WAAW,MAAM,IAAI,SAAS,EAAE;gBAChC,UAAU,KAAK,IAAI,QAAQ,EAAE;gBAC7B,cAAc,IAAI,IAAI,QAAQ,EAAE;gBAChC,WAAW,MAAM,IAAI,QAAQ,EAAE;aAChC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,OAAO;gBACL,UAAU;gBACV,aAAa,EAAE,UAAU;aAC1B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,UAAU,EAAE,MAAM;YAClB,aAAa,EAAE,MAAM;SACtB,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,YAAY;IACf,KAAK,GAAsB,IAAI,GAAG,EAAE,CAAC;IACrC,MAAM,CAAS;IAEvB,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,IAAU;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,mEAAmE;YACnE,OAAO,CAAC,IAAI,CACV,mBAAmB,IAAI,CAAC,IAAI,uCAAuC,CACpE,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,yCAAyC;QACzC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,YAAY,cAAc,IAAI,IAAI,YAAY,iBAAiB,EAAE,CAAC;gBACxE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAEjD,kDAAkD;QAClD,MAAM,gBAAgB,CACpB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,EACjC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,EACjC,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAC3B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mCAAmC;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;YACrC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CACb,8DAA8D,CAC/D,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAa,CAAC,CAAC;YACzE,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,iBAAiB,GAAG,KAAK,CAAC;YAC9B,MAAM,eAAe,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,aAAa;YACvD,MAAM,eAAe,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,aAAa;YAEvD,IAAI,gBAAgB,GAAG,CAAC,CAAC;YACzB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YAEzB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC9B,IAAI,iBAAiB;oBAAE,OAAO;gBAC9B,IAAI,gBAAgB,GAAG,IAAI,CAAC,MAAM,GAAG,eAAe,EAAE,CAAC;oBACrD,iBAAiB,GAAG,IAAI,CAAC;oBACzB,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,OAAO;gBACT,CAAC;gBACD,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC;gBAChC,MAAM,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC9B,IAAI,iBAAiB;oBAAE,OAAO;gBAC9B,IAAI,gBAAgB,GAAG,IAAI,CAAC,MAAM,GAAG,eAAe,EAAE,CAAC;oBACrD,iBAAiB,GAAG,IAAI,CAAC;oBACzB,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,OAAO;gBACT,CAAC;gBACD,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC;gBAChC,MAAM,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC1C,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACzB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;oBACxB,MAAM,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC;oBAC9B,MAAM,IAAI,aAAa,CAAC,GAAG,EAAE,CAAC;oBAE9B,IAAI,iBAAiB,EAAE,CAAC;wBACtB,OAAO,MAAM,CACX,IAAI,KAAK,CACP,wDAAwD,eAAe,SAAS,CACjF,CACF,CAAC;oBACJ,CAAC;oBAED,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;wBAClD,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBACtB,OAAO,MAAM,CACX,IAAI,KAAK,CAAC,gDAAgD,IAAI,EAAE,CAAC,CAClE,CAAC;oBACJ,CAAC;oBACD,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,0FAA0F;YAC1F,MAAM,SAAS,GAA0B,EAAE,CAAC;YAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAElD,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;gBACxD,MAAM,IAAI,KAAK,CACb,8DAA8D,CAC/D,CAAC;YACJ,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;gBACnC,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC;wBACjD,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;oBACnD,CAAC;yBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC;wBACvD,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;oBAClD,CAAC;yBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;wBACxB,SAAS,CAAC,IAAI,CAAC,IAA2B,CAAC,CAAC;oBAC9C,CAAC;gBACH,CAAC;YACH,CAAC;YACD,mCAAmC;YACnC,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;oBAC1D,SAAS;gBACX,CAAC;gBACD,uDAAuD;gBACvD,MAAM,UAAU,GACd,IAAI,CAAC,UAAU;oBACf,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;oBACnC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC7B,CAAC,CAAE,IAAI,CAAC,UAAqB;oBAC7B,CAAC,CAAC,EAAE,CAAC;gBACT,kBAAkB,CAAC,UAAU,CAAC,CAAC;gBAC/B,IAAI,CAAC,YAAY,CACf,IAAI,cAAc,CAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,WAAW,IAAI,EAAE,EACtB,UAAqC,CACtC,CACF,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,2BAA2B,YAAY,WAAW,EAAE,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,uBAAuB;QACrB,MAAM,YAAY,GAA0B,EAAE,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,UAAkB;QACjC,MAAM,WAAW,GAAW,EAAE,CAAC;QAC/B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,IAAK,IAA0B,EAAE,UAAU,KAAK,UAAU,EAAE,CAAC;gBAC3D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAY;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;CACF;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,kBAAkB,CAAC,MAAe;IAChD,mBAAmB,CAAC,MAAM,EAAE,IAAI,GAAG,EAAU,CAAC,CAAC;AACjD,CAAC;AAED;;;;GAIG;AACH,SAAS,mBAAmB,CAAC,MAA0B,EAAE,OAAoB;IAC3E,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QACnC,OAAO;IACT,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAEpB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,6DAA6D;QAC7D,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;QAC3B,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC9B,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,MAAM,CAAC,KAAK,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;QACtD,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IACD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;QACtB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACpD,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC9B,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IACD,oEAAoE;IACpE,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QAChC,IACE,MAAM,CAAC,MAAM;YACb,MAAM,CAAC,MAAM,KAAK,MAAM;YACxB,MAAM,CAAC,MAAM,KAAK,WAAW,EAC7B,CAAC;YACD,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QAC5B,CAAC;IACH,CAAC;AACH,CAAC"}