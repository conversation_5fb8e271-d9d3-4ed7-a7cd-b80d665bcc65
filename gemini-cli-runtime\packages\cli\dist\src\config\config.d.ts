/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Config, FileDiscoveryService } from '@google/gemini-cli-core';
import { Settings } from './settings.js';
import { Extension } from './extension.js';
export interface CliArgs {
    model: string | undefined;
    sandbox: boolean | string | undefined;
    sandboxImage: string | undefined;
    debug: boolean | undefined;
    prompt: string | undefined;
    promptInteractive: string | undefined;
    allFiles: boolean | undefined;
    all_files: boolean | undefined;
    showMemoryUsage: boolean | undefined;
    show_memory_usage: boolean | undefined;
    yolo: boolean | undefined;
    telemetry: boolean | undefined;
    checkpointing: boolean | undefined;
    telemetryTarget: string | undefined;
    telemetryOtlpEndpoint: string | undefined;
    telemetryLogPrompts: boolean | undefined;
    allowedMcpServerNames: string[] | undefined;
    extensions: string[] | undefined;
    listExtensions: boolean | undefined;
}
export declare function parseArguments(): Promise<CliArgs>;
export declare function loadHierarchicalGeminiMemory(currentWorkingDirectory: string, debugMode: boolean, fileService: FileDiscoveryService, extensionContextFilePaths?: string[]): Promise<{
    memoryContent: string;
    fileCount: number;
}>;
export declare function loadCliConfig(settings: Settings, extensions: Extension[], sessionId: string, argv: CliArgs): Promise<Config>;
