{"version": 3, "file": "setup.test.js", "sourceRoot": "", "sources": ["../../../src/code_assist/setup.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAC;AAC/D,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAE5D,OAAO,EAAkB,UAAU,EAAE,MAAM,YAAY,CAAC;AAExD,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAEpC,MAAM,YAAY,GAAmB;IACnC,EAAE,EAAE,UAAU,CAAC,QAAQ;IACvB,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,WAAW;CACzB,CAAC;AAEF,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACzB,IAAI,QAAkC,CAAC;IACvC,IAAI,eAAyC,CAAC;IAE9C,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QACnB,QAAQ,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QACnB,eAAe,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;YAC1C,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE;gBACR,uBAAuB,EAAE;oBACvB,EAAE,EAAE,gBAAgB;iBACrB;aACF;SACF,CAAC,CAAC;QACH,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,kBAAkB,CAC5C,GAAG,EAAE,CACH,CAAC;YACC,cAAc,EAAE,QAAQ;YACxB,WAAW,EAAE,eAAe;SAC7B,CAAgC,CACpC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACxD,OAAO,CAAC,GAAG,CAAC,oBAAoB,GAAG,cAAc,CAAC;QAClD,QAAQ,CAAC,iBAAiB,CAAC;YACzB,WAAW,EAAE,YAAY;SAC1B,CAAC,CAAC;QACH,MAAM,SAAS,CAAC,EAAkB,CAAC,CAAC;QACpC,MAAM,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAC3C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,cAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kFAAkF,EAAE,KAAK,IAAI,EAAE;QAChG,OAAO,CAAC,GAAG,CAAC,oBAAoB,GAAG,EAAE,CAAC;QACtC,QAAQ,CAAC,iBAAiB,CAAC;YACzB,uBAAuB,EAAE,gBAAgB;YACzC,WAAW,EAAE,YAAY;SAC1B,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,EAAkB,CAAC,CAAC;QACtD,MAAM,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAC3C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,SAAS,CACV,CAAC;QACF,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qEAAqE,EAAE,KAAK,IAAI,EAAE;QACnF,OAAO,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACxC,yDAAyD;QACzD,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE;YAClD,MAAM,IAAI,sBAAsB,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,SAAS,CAAC,EAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACzD,sBAAsB,CACvB,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}